{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/src/lib/api.ts"], "sourcesContent": ["import { AuthTokens, LoginResponse, User, ApiError } from '@/types/api';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\n\n// Token management\nexport const getTokens = (): AuthTokens => {\n  if (typeof window === 'undefined') return { access: '', refresh: '' };\n  \n  const access = localStorage.getItem('access_token') || '';\n  const refresh = localStorage.getItem('refresh_token') || '';\n  return { access, refresh };\n};\n\nexport const setTokens = (tokens: AuthTokens): void => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.setItem('access_token', tokens.access);\n  localStorage.setItem('refresh_token', tokens.refresh);\n};\n\nexport const clearTokens = (): void => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.removeItem('access_token');\n  localStorage.removeItem('refresh_token');\n};\n\n// Token refresh function\nconst refreshToken = async (): Promise<string | null> => {\n  const { refresh } = getTokens();\n  if (!refresh) return null;\n\n  try {\n    const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ refresh }),\n    });\n\n    if (response.ok) {\n      const data = await response.json();\n      setTokens({ access: data.access, refresh });\n      return data.access;\n    } else {\n      clearTokens();\n      return null;\n    }\n  } catch (error) {\n    console.error('Token refresh failed:', error);\n    clearTokens();\n    return null;\n  }\n};\n\n// API request wrapper with automatic token refresh\nexport const apiRequest = async <T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;\n\n  const { access } = getTokens();\n  const headers: Record<string, string> = {\n    'Content-Type': 'application/json',\n    ...(options.headers as Record<string, string>),\n  };\n\n  if (access) {\n    headers.Authorization = `Bearer ${access}`;\n  }\n\n  console.log(`API Request: ${options.method || 'GET'} ${url}`);\n  if (options.body) {\n    console.log('Request body:', options.body);\n  }\n\n  let response = await fetch(url, {\n    ...options,\n    headers,\n  });\n\n  // If token expired, try to refresh and retry\n  if (response.status === 401 && access) {\n    console.log('Token expired, attempting refresh...');\n    const newToken = await refreshToken();\n    \n    if (newToken) {\n      headers.Authorization = `Bearer ${newToken}`;\n      response = await fetch(url, {\n        ...options,\n        headers,\n      });\n    } else {\n      // Redirect to login if refresh fails\n      if (typeof window !== 'undefined') {\n        window.location.href = '/auth/login';\n      }\n      throw new Error('Authentication failed');\n    }\n  }\n\n  const data = await response.json();\n  console.log(`API Response: ${response.status}`, data);\n\n  if (!response.ok) {\n    const error: ApiError = {\n      message: data.message || data.error || 'An error occurred',\n      details: data.details || data,\n      status: response.status,\n    };\n    throw error;\n  }\n\n  return data;\n};\n\n// Authentication API\nexport const authApi = {\n  // Staff login with email/password\n  loginStaff: (credentials: { email: string; password: string }) =>\n    apiRequest<LoginResponse>('/auth/login/email/', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    }),\n\n  // Get user profile\n  getProfile: () =>\n    apiRequest<User>('/auth/profile/'),\n\n  // Update user profile\n  updateProfile: (data: Partial<User>) =>\n    apiRequest<User>('/auth/profile/', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Change password\n  changePassword: (data: { old_password: string; new_password: string; confirm_password: string }) =>\n    apiRequest('/auth/change-password/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Logout\n  logout: () =>\n    apiRequest('/auth/logout/', {\n      method: 'POST',\n    }),\n};\n\n// Orders API\nexport const ordersApi = {\n  // Get all orders (staff can see all)\n  getOrders: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/orders/${query}`);\n  },\n\n  // Get order details\n  getOrderDetail: (orderNumber: string) =>\n    apiRequest(`/orders/${orderNumber}/`),\n\n  // Update order status\n  updateOrderStatus: (orderNumber: string, data: { status: string; admin_notes?: string }) =>\n    apiRequest(`/orders/${orderNumber}/status/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Assign provider to order\n  assignProvider: (orderNumber: string, data: { provider_id: number; admin_notes?: string }) =>\n    apiRequest(`/orders/${orderNumber}/assign-provider/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Cancel order\n  cancelOrder: (orderNumber: string, data: { reason: string; admin_notes?: string }) =>\n    apiRequest(`/orders/${orderNumber}/cancel/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Users API\nexport const usersApi = {\n  // Get all users\n  getUsers: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/auth/users/${query}`);\n  },\n\n  // Get user details\n  getUserDetail: (userId: number) =>\n    apiRequest(`/auth/users/${userId}/`),\n\n  // Update user\n  updateUser: (userId: number, data: Partial<User>) =>\n    apiRequest(`/auth/users/${userId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Lock/unlock user\n  toggleUserLock: (userId: number, data: { is_locked: boolean; lockout_duration?: number }) =>\n    apiRequest(`/auth/users/${userId}/toggle-lock/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Providers API\nexport const providersApi = {\n  // Get all providers\n  getProviders: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/providers/${query}`);\n  },\n\n  // Get provider details\n  getProviderDetail: (providerId: number) =>\n    apiRequest(`/providers/${providerId}/`),\n\n  // Update provider verification status\n  updateProviderVerification: (providerId: number, data: { verification_status: string; admin_notes?: string }) =>\n    apiRequest(`/providers/${providerId}/verification/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Toggle provider availability\n  toggleProviderAvailability: (providerId: number, data: { is_available: boolean }) =>\n    apiRequest(`/providers/${providerId}/availability/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Services API\nexport const servicesApi = {\n  // Get all services\n  getServices: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/catalogue/services/${query}`);\n  },\n\n  // Get service details\n  getServiceDetail: (serviceId: number) =>\n    apiRequest(`/catalogue/services/${serviceId}/`),\n\n  // Create service\n  createService: (data: any) =>\n    apiRequest('/catalogue/services/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update service\n  updateService: (serviceId: number, data: any) =>\n    apiRequest(`/catalogue/services/${serviceId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete service\n  deleteService: (serviceId: number) =>\n    apiRequest(`/catalogue/services/${serviceId}/`, {\n      method: 'DELETE',\n    }),\n};\n\n// Categories API\nexport const categoriesApi = {\n  // Get all categories\n  getCategories: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/catalogue/categories/${query}`);\n  },\n\n  // Get category details\n  getCategoryDetail: (categoryId: number) =>\n    apiRequest(`/catalogue/categories/${categoryId}/`),\n\n  // Create category\n  createCategory: (data: any) =>\n    apiRequest('/catalogue/categories/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update category\n  updateCategory: (categoryId: number, data: any) =>\n    apiRequest(`/catalogue/categories/${categoryId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete category\n  deleteCategory: (categoryId: number) =>\n    apiRequest(`/catalogue/categories/${categoryId}/`, {\n      method: 'DELETE',\n    }),\n};\n\n// Payments API\nexport const paymentsApi = {\n  // Get all transactions\n  getTransactions: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/payments/transactions/${query}`);\n  },\n\n  // Get transaction details\n  getTransactionDetail: (transactionId: string) =>\n    apiRequest(`/payments/transactions/${transactionId}/`),\n\n  // Process refund\n  processRefund: (transactionId: string, data: { amount?: string; reason: string }) =>\n    apiRequest(`/payments/transactions/${transactionId}/refund/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Analytics API\nexport const analyticsApi = {\n  // Get dashboard stats (using orders dashboard endpoint)\n  getDashboardStats: () =>\n    apiRequest('/orders/dashboard/'),\n\n  // Get user statistics (using auth admin endpoint)\n  getUserStats: () =>\n    apiRequest('/auth/admin/user-stats/'),\n};\n\n// Scheduling API\nexport const schedulingApi = {\n  // Get time slots\n  getTimeSlots: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/scheduling/slots/${query}`);\n  },\n\n  // Create time slot\n  createTimeSlot: (data: any) =>\n    apiRequest('/scheduling/slots/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update time slot\n  updateTimeSlot: (slotId: number, data: any) =>\n    apiRequest(`/scheduling/slots/${slotId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Block time slot\n  blockTimeSlot: (slotId: number, data: { reason: string; blocked_by: string }) =>\n    apiRequest(`/scheduling/slots/${slotId}/block/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Get slot bookings\n  getSlotBookings: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/scheduling/bookings/${query}`);\n  },\n};\n\n// Coupons API\nexport const couponsApi = {\n  // Get all coupons\n  getCoupons: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/coupons/${query}`);\n  },\n\n  // Get coupon details\n  getCouponDetail: (couponId: number) =>\n    apiRequest(`/coupons/${couponId}/`),\n\n  // Create coupon\n  createCoupon: (data: any) =>\n    apiRequest('/coupons/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update coupon\n  updateCoupon: (couponId: number, data: any) =>\n    apiRequest(`/coupons/${couponId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete coupon\n  deleteCoupon: (couponId: number) =>\n    apiRequest(`/coupons/${couponId}/`, {\n      method: 'DELETE',\n    }),\n\n  // Toggle coupon status\n  toggleCouponStatus: (couponId: number, data: { is_active: boolean }) =>\n    apiRequest(`/coupons/${couponId}/toggle/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEqB;AAArB,MAAM,eAAe,iEAAwC;AAGtD,MAAM,YAAY;IACvB,uCAAmC;;IAAkC;IAErE,MAAM,SAAS,aAAa,OAAO,CAAC,mBAAmB;IACvD,MAAM,UAAU,aAAa,OAAO,CAAC,oBAAoB;IACzD,OAAO;QAAE;QAAQ;IAAQ;AAC3B;AAEO,MAAM,YAAY,CAAC;IACxB,uCAAmC;;IAAM;IAEzC,aAAa,OAAO,CAAC,gBAAgB,OAAO,MAAM;IAClD,aAAa,OAAO,CAAC,iBAAiB,OAAO,OAAO;AACtD;AAEO,MAAM,cAAc;IACzB,uCAAmC;;IAAM;IAEzC,aAAa,UAAU,CAAC;IACxB,aAAa,UAAU,CAAC;AAC1B;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;gBAAE,QAAQ,KAAK,MAAM;gBAAE;YAAQ;YACzC,OAAO,KAAK,MAAM;QACpB,OAAO;YACL;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC;QACA,OAAO;IACT;AACF;AAGO,MAAM,aAAa,OACxB,UACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,eAAe,UAAU;IAEjF,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,UAAkC;QACtC,gBAAgB;QAChB,GAAI,QAAQ,OAAO;IACrB;IAEA,IAAI,QAAQ;QACV,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ;IAC5C;IAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK;IAC5D,IAAI,QAAQ,IAAI,EAAE;QAChB,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,IAAI;IAC3C;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK;QAC9B,GAAG,OAAO;QACV;IACF;IAEA,6CAA6C;IAC7C,IAAI,SAAS,MAAM,KAAK,OAAO,QAAQ;QACrC,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM;QAEvB,IAAI,UAAU;YACZ,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU;YAC5C,WAAW,MAAM,MAAM,KAAK;gBAC1B,GAAG,OAAO;gBACV;YACF;QACF,OAAO;YACL,qCAAqC;YACrC,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YACA,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE,EAAE;IAEhD,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAkB;YACtB,SAAS,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;YACvC,SAAS,KAAK,OAAO,IAAI;YACzB,QAAQ,SAAS,MAAM;QACzB;QACA,MAAM;IACR;IAEA,OAAO;AACT;AAGO,MAAM,UAAU;IACrB,kCAAkC;IAClC,YAAY,CAAC,cACX,WAA0B,sBAAsB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,mBAAmB;IACnB,YAAY,IACV,WAAiB;IAEnB,sBAAsB;IACtB,eAAe,CAAC,OACd,WAAiB,kBAAkB;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,OACf,WAAW,0BAA0B;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,SAAS;IACT,QAAQ,IACN,WAAW,iBAAiB;YAC1B,QAAQ;QACV;AACJ;AAGO,MAAM,YAAY;IACvB,qCAAqC;IACrC,WAAW,CAAC;QACV,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,QAAQ,EAAE,OAAO;IACtC;IAEA,oBAAoB;IACpB,gBAAgB,CAAC,cACf,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAEtC,sBAAsB;IACtB,mBAAmB,CAAC,aAAqB,OACvC,WAAW,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,EAAE;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,2BAA2B;IAC3B,gBAAgB,CAAC,aAAqB,OACpC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,CAAC,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,eAAe;IACf,aAAa,CAAC,aAAqB,OACjC,WAAW,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,EAAE;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,WAAW;IACtB,gBAAgB;IAChB,UAAU,CAAC;QACT,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,YAAY,EAAE,OAAO;IAC1C;IAEA,mBAAmB;IACnB,eAAe,CAAC,SACd,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAErC,cAAc;IACd,YAAY,CAAC,QAAgB,OAC3B,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,mBAAmB;IACnB,gBAAgB,CAAC,QAAgB,OAC/B,WAAW,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC,EAAE;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,eAAe;IAC1B,oBAAoB;IACpB,cAAc,CAAC;QACb,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,WAAW,EAAE,OAAO;IACzC;IAEA,uBAAuB;IACvB,mBAAmB,CAAC,aAClB,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAExC,sCAAsC;IACtC,4BAA4B,CAAC,YAAoB,OAC/C,WAAW,CAAC,WAAW,EAAE,WAAW,cAAc,CAAC,EAAE;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,+BAA+B;IAC/B,4BAA4B,CAAC,YAAoB,OAC/C,WAAW,CAAC,WAAW,EAAE,WAAW,cAAc,CAAC,EAAE;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,cAAc;IACzB,mBAAmB;IACnB,aAAa,CAAC;QACZ,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,oBAAoB,EAAE,OAAO;IAClD;IAEA,sBAAsB;IACtB,kBAAkB,CAAC,YACjB,WAAW,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;IAEhD,iBAAiB;IACjB,eAAe,CAAC,OACd,WAAW,wBAAwB;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,iBAAiB;IACjB,eAAe,CAAC,WAAmB,OACjC,WAAW,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,iBAAiB;IACjB,eAAe,CAAC,YACd,WAAW,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,EAAE;YAC9C,QAAQ;QACV;AACJ;AAGO,MAAM,gBAAgB;IAC3B,qBAAqB;IACrB,eAAe,CAAC;QACd,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,sBAAsB,EAAE,OAAO;IACpD;IAEA,uBAAuB;IACvB,mBAAmB,CAAC,aAClB,WAAW,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;IAEnD,kBAAkB;IAClB,gBAAgB,CAAC,OACf,WAAW,0BAA0B;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,YAAoB,OACnC,WAAW,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,aACf,WAAW,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;YACjD,QAAQ;QACV;AACJ;AAGO,MAAM,cAAc;IACzB,uBAAuB;IACvB,iBAAiB,CAAC;QAChB,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,uBAAuB,EAAE,OAAO;IACrD;IAEA,0BAA0B;IAC1B,sBAAsB,CAAC,gBACrB,WAAW,CAAC,uBAAuB,EAAE,cAAc,CAAC,CAAC;IAEvD,iBAAiB;IACjB,eAAe,CAAC,eAAuB,OACrC,WAAW,CAAC,uBAAuB,EAAE,cAAc,QAAQ,CAAC,EAAE;YAC5D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,eAAe;IAC1B,wDAAwD;IACxD,mBAAmB,IACjB,WAAW;IAEb,kDAAkD;IAClD,cAAc,IACZ,WAAW;AACf;AAGO,MAAM,gBAAgB;IAC3B,iBAAiB;IACjB,cAAc,CAAC;QACb,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,kBAAkB,EAAE,OAAO;IAChD;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,OACf,WAAW,sBAAsB;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,mBAAmB;IACnB,gBAAgB,CAAC,QAAgB,OAC/B,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,eAAe,CAAC,QAAgB,OAC9B,WAAW,CAAC,kBAAkB,EAAE,OAAO,OAAO,CAAC,EAAE;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,oBAAoB;IACpB,iBAAiB,CAAC;QAChB,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,qBAAqB,EAAE,OAAO;IACnD;AACF;AAGO,MAAM,aAAa;IACxB,kBAAkB;IAClB,YAAY,CAAC;QACX,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,SAAS,EAAE,OAAO;IACvC;IAEA,qBAAqB;IACrB,iBAAiB,CAAC,WAChB,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAEpC,gBAAgB;IAChB,cAAc,CAAC,OACb,WAAW,aAAa;YACtB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,gBAAgB;IAChB,cAAc,CAAC,UAAkB,OAC/B,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,gBAAgB;IAChB,cAAc,CAAC,WACb,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;YAClC,QAAQ;QACV;IAEF,uBAAuB;IACvB,oBAAoB,CAAC,UAAkB,OACrC,WAAW,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { User, AuthTokens, LoginResponse } from '@/types/api';\nimport { authApi, setTokens, getTokens, clearTokens } from '@/lib/api';\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  logout: () => Promise<void>;\n  updateUser: (userData: Partial<User>) => void;\n  refreshUserProfile: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const isAuthenticated = !!user;\n\n  // Initialize auth state on mount\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const { refresh } = getTokens();\n      \n      if (refresh) {\n        try {\n          // Try to get user profile to verify token validity\n          const userProfile = await authApi.getProfile();\n          \n          // Ensure user is staff\n          if (userProfile.user_type !== 'STAFF') {\n            clearTokens();\n            throw new Error('Access denied: Staff access required');\n          }\n          \n          setUser(userProfile as User);\n        } catch (error) {\n          // Token is invalid, clear it\n          console.error('Auth initialization failed:', error);\n          clearTokens();\n        }\n      }\n      \n      setIsLoading(false);\n    };\n\n    initializeAuth();\n  }, []);\n\n  const login = async (credentials: { email: string; password: string }) => {\n    try {\n      const response = await authApi.loginStaff(credentials);\n      \n      // Ensure user is staff\n      if (response.user.user_type !== 'STAFF') {\n        throw new Error('Access denied: Staff access required');\n      }\n      \n      setTokens(response.tokens);\n      setUser(response.user);\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authApi.logout();\n    } catch (error) {\n      // Even if logout fails on server, clear local tokens\n      console.error('Logout error:', error);\n    } finally {\n      clearTokens();\n      setUser(null);\n    }\n  };\n\n  const updateUser = (userData: Partial<User>) => {\n    if (user) {\n      setUser({ ...user, ...userData });\n    }\n  };\n\n  const refreshUserProfile = async () => {\n    if (isAuthenticated) {\n      try {\n        const userProfile = await authApi.getProfile();\n        setUser(userProfile as User);\n      } catch (error) {\n        console.error('Failed to refresh user profile:', error);\n      }\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    isAuthenticated,\n    isLoading,\n    login,\n    logout,\n    updateUser,\n    refreshUserProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC,CAAC;IAE1B,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB;oBACrB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;oBAE5B,IAAI,SAAS;wBACX,IAAI;4BACF,mDAAmD;4BACnD,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU;4BAE5C,uBAAuB;4BACvB,IAAI,YAAY,SAAS,KAAK,SAAS;gCACrC,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;gCACV,MAAM,IAAI,MAAM;4BAClB;4BAEA,QAAQ;wBACV,EAAE,OAAO,OAAO;4BACd,6BAA6B;4BAC7B,QAAQ,KAAK,CAAC,+BAA+B;4BAC7C,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;wBACZ;oBACF;oBAEA,aAAa;gBACf;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YAE1C,uBAAuB;YACvB,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,SAAS;gBACvC,MAAM,IAAI,MAAM;YAClB;YAEA,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM;YACzB,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,qDAAqD;YACrD,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;YACV,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM;YACR,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC;QACjC;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB;YACnB,IAAI;gBACF,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU;gBAC5C,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAjGa;KAAA", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}