(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{858:(e,s,t)=>{"use strict";t.d(s,{N:()=>C});var r=t(5155),a=t(2115),l=t(6874),n=t.n(l),c=t(5695),i=t(7340),d=t(6151),x=t(7580),o=t(5670),m=t(7108),h=t(3332),u=t(1586),g=t(9074),y=t(2713),f=t(381),j=t(4416),p=t(1007),v=t(4835),b=t(4783),N=t(9434),w=t(283),A=t(3741);let k=[{name:"Dashboard",href:"/dashboard",icon:i.A},{name:"Orders",href:"/orders",icon:d.A},{name:"Customers",href:"/customers",icon:x.A},{name:"Providers",href:"/providers",icon:o.A},{name:"Services",href:"/services",icon:m.A},{name:"Categories",href:"/categories",icon:h.A},{name:"Payments",href:"/payments",icon:u.A},{name:"Scheduling",href:"/scheduling",icon:g.A},{name:"Analytics",href:"/analytics",icon:y.A},{name:"Settings",href:"/settings",icon:f.A}],C=e=>{let{children:s}=e,[t,l]=(0,a.useState)(!1),i=(0,c.usePathname)(),{user:d,logout:x}=(0,w.A)(),o=async()=>{try{await x()}catch(e){console.error("Logout failed:",e)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>l(!1)}),(0,r.jsx)("div",{className:(0,N.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",t?"translate-x-0":"-translate-x-full"),children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Staff Dashboard"}),(0,r.jsx)("button",{onClick:()=>l(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,r.jsx)(j.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:k.map(e=>{let s=i===e.href||i.startsWith(e.href+"/");return(0,r.jsxs)(n(),{href:e.href,className:(0,N.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>l(!1),children:[(0,r.jsx)(e.icon,{className:"w-5 h-5 mr-3"}),e.name]},e.name)})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-blue-600"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:null==d?void 0:d.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:null==d?void 0:d.email})]})]}),(0,r.jsxs)(A.$,{variant:"outline",size:"sm",fullWidth:!0,onClick:o,className:"text-red-600 border-red-200 hover:bg-red-50",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[(0,r.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,r.jsx)("button",{onClick:()=>l(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,r.jsx)(b.A,{className:"w-5 h-5"})}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",null==d?void 0:d.name]})})]})}),(0,r.jsx)("main",{className:"p-6",children:s})]})]})}},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2814:(e,s,t)=>{"use strict";t.d(s,{E:()=>l,W:()=>n});var r=t(5155);t(2115);var a=t(9434);let l=e=>{let{children:s,variant:t="default",size:l="md",className:n}=e;return(0,r.jsx)("span",{className:(0,a.cn)("inline-flex items-center font-medium rounded-full",{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800"}[t],{sm:"px-2 py-1 text-xs",md:"px-2.5 py-0.5 text-sm",lg:"px-3 py-1 text-base"}[l],n),children:s})},n=e=>{let{status:s,className:t}=e;return(0,r.jsx)(l,{variant:{pending:"warning",confirmed:"info",assigned:"info",in_progress:"info",completed:"success",cancelled:"error",refunded:"default",paid:"success",failed:"error",active:"success",inactive:"default",locked:"error",verified:"success",rejected:"error",available:"success",unavailable:"error",blocked:"error"}[s.toLowerCase()]||"default",className:t,children:s.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ")})}},3333:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(5155),a=t(2115),l=t(4616),n=t(7924),c=t(2657),i=t(3717);let d=(0,t(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var x=t(858),o=t(7703),m=t(3741),h=t(2814),u=t(4338),g=t(5731),y=t(9434);function f(){let[e,s]=(0,a.useState)([]),[t,f]=(0,a.useState)(!0),[j,p]=(0,a.useState)(""),[v,b]=(0,a.useState)(""),[N,w]=(0,a.useState)(""),[A,k]=(0,a.useState)({count:0,next:null,previous:null});(0,a.useEffect)(()=>{C()},[v,N]);let C=async()=>{try{f(!0);let e={ordering:"-created_at",limit:20};v&&(e.search=v),N&&(e.status=N);let t=await g.cN.getOrders(e);s(t.results),k({count:t.count,next:t.next,previous:t.previous})}catch(e){p(e.message||"Failed to load orders"),console.error("Orders fetch error:",e)}finally{f(!1)}};return t?(0,r.jsx)(x.N,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Orders"}),(0,r.jsx)(u.B0,{})]})}):(0,r.jsx)(x.N,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Orders"}),(0,r.jsxs)(m.$,{children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Create Order"]})]}),(0,r.jsx)(o.Zp,{children:(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search orders...",value:v,onChange:e=>b(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})}),(0,r.jsx)("div",{className:"sm:w-48",children:(0,r.jsxs)("select",{value:N,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"confirmed",children:"Confirmed"}),(0,r.jsx)("option",{value:"assigned",children:"Assigned"}),(0,r.jsx)("option",{value:"in_progress",children:"In Progress"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelled"})]})})]})})}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{children:["Orders (",A.count,")"]})}),(0,r.jsx)(o.Wu,{children:j?(0,r.jsx)("div",{className:"text-center py-8 text-red-600",children:j}):0===e.length?(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No orders found"}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Order"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Customer"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Amount"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Date"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:["#",e.order_number]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.items_count," items"]})]})}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.customer_name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.customer_mobile})]})}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)(h.W,{status:e.status})}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:(0,y.vv)(e.total_amount)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.payment_method.toUpperCase()})]}),(0,r.jsxs)("td",{className:"py-3 px-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-900",children:(0,y.r6)(e.created_at)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:(0,y.fw)(e.created_at)})]}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})}),(0,r.jsx)(m.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(i.A,{className:"w-4 h-4"})}),(0,r.jsx)(m.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(d,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]}),A.count>20&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",e.length," of ",A.count," orders"]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(m.$,{variant:"outline",size:"sm",disabled:!A.previous,children:"Previous"}),(0,r.jsx)(m.$,{variant:"outline",size:"sm",disabled:!A.next,children:"Next"})]})]})]})})}},3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3741:(e,s,t)=>{"use strict";t.d(s,{$:()=>n});var r=t(5155);t(2115);var a=t(9434),l=t(4338);let n=e=>{let{children:s,variant:t="primary",size:n="md",loading:c=!1,disabled:i=!1,className:d,onClick:x,type:o="button",fullWidth:m=!1}=e;return(0,r.jsxs)("button",{type:o,onClick:x,disabled:i||c,className:(0,a.cn)("inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],m&&"w-full",d),children:[c&&(0,r.jsx)(l.kt,{size:"sm",className:"mr-2"}),s]})}},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4958:(e,s,t)=>{Promise.resolve().then(t.bind(t,3333))},7703:(e,s,t)=>{"use strict";t.d(s,{CN:()=>d,Wu:()=>i,ZB:()=>c,Zp:()=>l,aR:()=>n});var r=t(5155);t(2115);var a=t(9434);let l=e=>{let{children:s,className:t,padding:l="md"}=e;return(0,r.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow border border-gray-200",{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[l],t),children:s})},n=e=>{let{children:s,className:t}=e;return(0,r.jsx)("div",{className:(0,a.cn)("border-b border-gray-200 pb-4 mb-4",t),children:s})},c=e=>{let{children:s,className:t}=e;return(0,r.jsx)("h3",{className:(0,a.cn)("text-lg font-semibold text-gray-900",t),children:s})},i=e=>{let{children:s,className:t}=e;return(0,r.jsx)("div",{className:(0,a.cn)("text-gray-600",t),children:s})},d=e=>{let{title:s,value:t,change:n,icon:c,className:i}=e;return(0,r.jsx)(l,{className:(0,a.cn)("",i),children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:s}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t}),n&&(0,r.jsxs)("p",{className:(0,a.cn)("text-sm font-medium","increase"===n.type?"text-green-600":"text-red-600"),children:["increase"===n.type?"↗":"↘"," ",n.value]})]}),c&&(0,r.jsx)("div",{className:"text-gray-400",children:c})]})})}},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[743,952,441,684,358],()=>s(4958)),_N_E=e.O()}]);