from django.urls import path
from . import views

app_name = 'providers'

urlpatterns = [
    # Provider list for staff (root endpoint)
    path('', views.AdminProviderListView.as_view(), name='provider-list'),

    # Provider profile management
    path('profile/', views.ProviderProfileView.as_view(), name='provider-profile'),
    path('profile/update/', views.UpdateProviderProfileView.as_view(), name='update-provider-profile'),
    
    # Document management
    path('documents/', views.ProviderDocumentListView.as_view(), name='provider-documents'),
    path('documents/upload/', views.upload_document, name='upload-document'),
    path('documents/<int:pk>/', views.ProviderDocumentDetailView.as_view(), name='document-detail'),
    
    # Bank details
    path('bank-details/', views.ProviderBankDetailsView.as_view(), name='provider-bank-details'),
    path('bank-details/update/', views.update_bank_details, name='update-bank-details'),
    
    # Availability management
    path('availability/', views.ProviderAvailabilityListView.as_view(), name='provider-availability'),
    path('availability/update/', views.update_availability, name='update-availability'),
    
    # Payout requests
    path('payouts/', views.ProviderPayoutListView.as_view(), name='provider-payouts'),
    path('payouts/request/', views.request_payout, name='request-payout'),
    path('payouts/<int:pk>/', views.ProviderPayoutDetailView.as_view(), name='payout-detail'),
    
    # Admin views
    path('admin/list/', views.AdminProviderListView.as_view(), name='admin-provider-list'),
    path('admin/<int:pk>/verify/', views.verify_provider, name='verify-provider'),
    path('admin/<int:pk>/documents/', views.AdminProviderDocumentsView.as_view(), name='admin-provider-documents'),
    path('admin/documents/<int:pk>/verify/', views.verify_document, name='verify-document'),
    path('admin/payouts/', views.AdminPayoutListView.as_view(), name='admin-payout-list'),
    path('admin/payouts/<int:pk>/process/', views.process_payout, name='process-payout'),
]
