[{"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\analytics\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\auth\\login\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\categories\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\customers\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\orders\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\payments\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\providers\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\scheduling\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\services\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\settings\\page.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\layout\\DashboardLayout.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\Badge.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\Button.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\Card.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\LoadingSpinner.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\contexts\\AuthContext.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\lib\\api.ts": "20", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\lib\\utils.ts": "21", "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\types\\api.ts": "22"}, {"size": 1013, "mtime": 1750328695053, "results": "23", "hashOfConfig": "24"}, {"size": 4616, "mtime": 1750327120961, "results": "25", "hashOfConfig": "24"}, {"size": 1364, "mtime": 1750328664582, "results": "26", "hashOfConfig": "24"}, {"size": 9968, "mtime": 1750330081142, "results": "27", "hashOfConfig": "24"}, {"size": 7601, "mtime": 1750330278245, "results": "28", "hashOfConfig": "24"}, {"size": 845, "mtime": 1750327264196, "results": "29", "hashOfConfig": "24"}, {"size": 9268, "mtime": 1750330333906, "results": "30", "hashOfConfig": "24"}, {"size": 593, "mtime": 1750327237929, "results": "31", "hashOfConfig": "24"}, {"size": 1023, "mtime": 1750328675437, "results": "32", "hashOfConfig": "24"}, {"size": 12055, "mtime": 1750330387020, "results": "33", "hashOfConfig": "24"}, {"size": 1007, "mtime": 1750328684932, "results": "34", "hashOfConfig": "24"}, {"size": 1356, "mtime": 1750328654649, "results": "35", "hashOfConfig": "24"}, {"size": 1013, "mtime": 1750328705231, "results": "36", "hashOfConfig": "24"}, {"size": 5271, "mtime": 1750327095465, "results": "37", "hashOfConfig": "24"}, {"size": 2341, "mtime": 1750327019301, "results": "38", "hashOfConfig": "24"}, {"size": 3512, "mtime": 1750327043465, "results": "39", "hashOfConfig": "24"}, {"size": 2742, "mtime": 1750327064704, "results": "40", "hashOfConfig": "24"}, {"size": 4307, "mtime": 1750326999353, "results": "41", "hashOfConfig": "24"}, {"size": 3302, "mtime": 1750326931652, "results": "42", "hashOfConfig": "24"}, {"size": 11533, "mtime": 1750328456242, "results": "43", "hashOfConfig": "24"}, {"size": 6807, "mtime": 1750329601838, "results": "44", "hashOfConfig": "24"}, {"size": 5811, "mtime": 1750328714334, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "17j1gw7", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\analytics\\page.tsx", ["112", "113", "114"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\auth\\login\\page.tsx", ["115", "116", "117"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\categories\\page.tsx", ["118", "119"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\customers\\page.tsx", ["120", "121", "122", "123", "124", "125"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\dashboard\\page.tsx", ["126", "127", "128", "129", "130", "131"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\orders\\page.tsx", ["132", "133", "134", "135", "136", "137"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\payments\\page.tsx", ["138", "139", "140"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\providers\\page.tsx", ["141", "142", "143", "144", "145", "146", "147", "148", "149", "150"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\scheduling\\page.tsx", ["151", "152", "153"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\services\\page.tsx", ["154", "155"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\app\\settings\\page.tsx", ["156", "157", "158"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\layout\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\Badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\contexts\\AuthContext.tsx", ["159", "160"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\lib\\api.ts", ["161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\lib\\utils.ts", ["178", "179"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\types\\api.ts", ["180", "181", "182"], [], {"ruleId": "183", "severity": 2, "message": "184", "line": 4, "column": 21, "nodeType": null, "messageId": "185", "endLine": 4, "endColumn": 31}, {"ruleId": "183", "severity": 2, "message": "186", "line": 6, "column": 16, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 6, "column": 28, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 37}, {"ruleId": "183", "severity": 2, "message": "186", "line": 8, "column": 16, "nodeType": null, "messageId": "185", "endLine": 8, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 8, "column": 28, "nodeType": null, "messageId": "185", "endLine": 8, "endColumn": 37}, {"ruleId": "188", "severity": 2, "message": "189", "line": 40, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 40, "endColumn": 22, "suggestions": "192"}, {"ruleId": "183", "severity": 2, "message": "186", "line": 6, "column": 16, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 6, "column": 28, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 37}, {"ruleId": "183", "severity": 2, "message": "193", "line": 6, "column": 3, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 9}, {"ruleId": "183", "severity": 2, "message": "194", "line": 9, "column": 3, "nodeType": null, "messageId": "185", "endLine": 9, "endColumn": 17}, {"ruleId": "195", "severity": 1, "message": "196", "line": 38, "column": 6, "nodeType": "197", "endLine": 38, "endColumn": 32, "suggestions": "198"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 43, "column": 36, "nodeType": "190", "messageId": "191", "endLine": 43, "endColumn": 39, "suggestions": "199"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 64, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 64, "endColumn": 22, "suggestions": "200"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 77, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 77, "endColumn": 22, "suggestions": "201"}, {"ruleId": "183", "severity": 2, "message": "202", "line": 6, "column": 3, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 8}, {"ruleId": "183", "severity": 2, "message": "203", "line": 7, "column": 3, "nodeType": null, "messageId": "185", "endLine": 7, "endColumn": 12}, {"ruleId": "183", "severity": 2, "message": "204", "line": 8, "column": 3, "nodeType": null, "messageId": "185", "endLine": 8, "endColumn": 13}, {"ruleId": "183", "severity": 2, "message": "205", "line": 18, "column": 24, "nodeType": null, "messageId": "185", "endLine": 18, "endColumn": 33}, {"ruleId": "188", "severity": 2, "message": "189", "line": 47, "column": 43, "nodeType": "190", "messageId": "191", "endLine": 47, "endColumn": 46, "suggestions": "206"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 48, "column": 21, "nodeType": "190", "messageId": "191", "endLine": 48, "endColumn": 24, "suggestions": "207"}, {"ruleId": "183", "severity": 2, "message": "193", "line": 6, "column": 3, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 9}, {"ruleId": "195", "severity": 1, "message": "208", "line": 35, "column": 6, "nodeType": "197", "endLine": 35, "endColumn": 32, "suggestions": "209"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 40, "column": 36, "nodeType": "190", "messageId": "191", "endLine": 40, "endColumn": 39, "suggestions": "210"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 60, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 60, "endColumn": 22, "suggestions": "211"}, {"ruleId": "183", "severity": 2, "message": "212", "line": 68, "column": 9, "nodeType": null, "messageId": "185", "endLine": 68, "endColumn": 27}, {"ruleId": "188", "severity": 2, "message": "189", "line": 73, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 73, "endColumn": 22, "suggestions": "213"}, {"ruleId": "183", "severity": 2, "message": "204", "line": 4, "column": 22, "nodeType": null, "messageId": "185", "endLine": 4, "endColumn": 32}, {"ruleId": "183", "severity": 2, "message": "186", "line": 6, "column": 16, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 6, "column": 28, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 37}, {"ruleId": "183", "severity": 2, "message": "193", "line": 6, "column": 3, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 9}, {"ruleId": "183", "severity": 2, "message": "214", "line": 8, "column": 3, "nodeType": null, "messageId": "185", "endLine": 8, "endColumn": 7}, {"ruleId": "183", "severity": 2, "message": "194", "line": 9, "column": 3, "nodeType": null, "messageId": "185", "endLine": 9, "endColumn": 17}, {"ruleId": "183", "severity": 2, "message": "215", "line": 24, "column": 10, "nodeType": null, "messageId": "185", "endLine": 24, "endColumn": 24}, {"ruleId": "183", "severity": 2, "message": "216", "line": 24, "column": 26, "nodeType": null, "messageId": "185", "endLine": 24, "endColumn": 44}, {"ruleId": "195", "severity": 1, "message": "217", "line": 41, "column": 6, "nodeType": "197", "endLine": 41, "endColumn": 32, "suggestions": "218"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 46, "column": 36, "nodeType": "190", "messageId": "191", "endLine": 46, "endColumn": 39, "suggestions": "219"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 66, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 66, "endColumn": 22, "suggestions": "220"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 81, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 81, "endColumn": 22, "suggestions": "221"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 94, "column": 19, "nodeType": "190", "messageId": "191", "endLine": 94, "endColumn": 22, "suggestions": "222"}, {"ruleId": "183", "severity": 2, "message": "223", "line": 4, "column": 20, "nodeType": null, "messageId": "185", "endLine": 4, "endColumn": 25}, {"ruleId": "183", "severity": 2, "message": "186", "line": 6, "column": 16, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 6, "column": 28, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 37}, {"ruleId": "183", "severity": 2, "message": "186", "line": 6, "column": 16, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 6, "column": 28, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 37}, {"ruleId": "183", "severity": 2, "message": "224", "line": 4, "column": 20, "nodeType": null, "messageId": "185", "endLine": 4, "endColumn": 24}, {"ruleId": "183", "severity": 2, "message": "186", "line": 6, "column": 16, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "187", "line": 6, "column": 28, "nodeType": null, "messageId": "185", "endLine": 6, "endColumn": 37}, {"ruleId": "183", "severity": 2, "message": "225", "line": 4, "column": 16, "nodeType": null, "messageId": "185", "endLine": 4, "endColumn": 26}, {"ruleId": "183", "severity": 2, "message": "226", "line": 4, "column": 28, "nodeType": null, "messageId": "185", "endLine": 4, "endColumn": 41}, {"ruleId": "188", "severity": 2, "message": "189", "line": 156, "column": 39, "nodeType": "190", "messageId": "191", "endLine": 156, "endColumn": 42, "suggestions": "227"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 190, "column": 38, "nodeType": "190", "messageId": "191", "endLine": 190, "endColumn": 41, "suggestions": "228"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 217, "column": 42, "nodeType": "190", "messageId": "191", "endLine": 217, "endColumn": 45, "suggestions": "229"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 244, "column": 41, "nodeType": "190", "messageId": "191", "endLine": 244, "endColumn": 44, "suggestions": "230"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 254, "column": 25, "nodeType": "190", "messageId": "191", "endLine": 254, "endColumn": 28, "suggestions": "231"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 261, "column": 44, "nodeType": "190", "messageId": "191", "endLine": 261, "endColumn": 47, "suggestions": "232"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 277, "column": 43, "nodeType": "190", "messageId": "191", "endLine": 277, "endColumn": 46, "suggestions": "233"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 287, "column": 26, "nodeType": "190", "messageId": "191", "endLine": 287, "endColumn": 29, "suggestions": "234"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 294, "column": 46, "nodeType": "190", "messageId": "191", "endLine": 294, "endColumn": 49, "suggestions": "235"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 310, "column": 45, "nodeType": "190", "messageId": "191", "endLine": 310, "endColumn": 48, "suggestions": "236"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 341, "column": 42, "nodeType": "190", "messageId": "191", "endLine": 341, "endColumn": 45, "suggestions": "237"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 347, "column": 26, "nodeType": "190", "messageId": "191", "endLine": 347, "endColumn": 29, "suggestions": "238"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 354, "column": 42, "nodeType": "190", "messageId": "191", "endLine": 354, "endColumn": 45, "suggestions": "239"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 368, "column": 45, "nodeType": "190", "messageId": "191", "endLine": 368, "endColumn": 48, "suggestions": "240"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 377, "column": 40, "nodeType": "190", "messageId": "191", "endLine": 377, "endColumn": 43, "suggestions": "241"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 387, "column": 24, "nodeType": "190", "messageId": "191", "endLine": 387, "endColumn": 27, "suggestions": "242"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 394, "column": 42, "nodeType": "190", "messageId": "191", "endLine": 394, "endColumn": 45, "suggestions": "243"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 157, "column": 46, "nodeType": "190", "messageId": "191", "endLine": 157, "endColumn": 49, "suggestions": "244"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 157, "column": 56, "nodeType": "190", "messageId": "191", "endLine": 157, "endColumn": 59, "suggestions": "245"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 111, "column": 21, "nodeType": "190", "messageId": "191", "endLine": 111, "endColumn": 24, "suggestions": "246"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 147, "column": 28, "nodeType": "190", "messageId": "191", "endLine": 147, "endColumn": 31, "suggestions": "247"}, {"ruleId": "188", "severity": 2, "message": "189", "line": 171, "column": 22, "nodeType": "190", "messageId": "191", "endLine": 171, "endColumn": 25, "suggestions": "248"}, "@typescript-eslint/no-unused-vars", "'TrendingUp' is defined but never used.", "unusedVar", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["249", "250"], "'Filter' is defined but never used.", "'MoreHorizontal' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCustomers'. Either include it or remove the dependency array.", "ArrayExpression", ["251"], ["252", "253"], ["254", "255"], ["256", "257"], "'Users' is defined but never used.", "'UserCheck' is defined but never used.", "'DollarSign' is defined but never used.", "'ordersApi' is defined but never used.", ["258", "259"], ["260", "261"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["262"], ["263", "264"], ["265", "266"], "'handleStatusChange' is assigned a value but never used.", ["267", "268"], "'Edit' is defined but never used.", "'formatDateTime' is defined but never used.", "'formatRelativeTime' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchProviders'. Either include it or remove the dependency array.", ["269"], ["270", "271"], ["272", "273"], ["274", "275"], ["276", "277"], "'Clock' is defined but never used.", "'User' is defined but never used.", "'AuthTokens' is defined but never used.", "'LoginResponse' is defined but never used.", ["278", "279"], ["280", "281"], ["282", "283"], ["284", "285"], ["286", "287"], ["288", "289"], ["290", "291"], ["292", "293"], ["294", "295"], ["296", "297"], ["298", "299"], ["300", "301"], ["302", "303"], ["304", "305"], ["306", "307"], ["308", "309"], ["310", "311"], ["312", "313"], ["314", "315"], ["316", "317"], ["318", "319"], ["320", "321"], {"messageId": "322", "fix": "323", "desc": "324"}, {"messageId": "325", "fix": "326", "desc": "327"}, {"desc": "328", "fix": "329"}, {"messageId": "322", "fix": "330", "desc": "324"}, {"messageId": "325", "fix": "331", "desc": "327"}, {"messageId": "322", "fix": "332", "desc": "324"}, {"messageId": "325", "fix": "333", "desc": "327"}, {"messageId": "322", "fix": "334", "desc": "324"}, {"messageId": "325", "fix": "335", "desc": "327"}, {"messageId": "322", "fix": "336", "desc": "324"}, {"messageId": "325", "fix": "337", "desc": "327"}, {"messageId": "322", "fix": "338", "desc": "324"}, {"messageId": "325", "fix": "339", "desc": "327"}, {"desc": "340", "fix": "341"}, {"messageId": "322", "fix": "342", "desc": "324"}, {"messageId": "325", "fix": "343", "desc": "327"}, {"messageId": "322", "fix": "344", "desc": "324"}, {"messageId": "325", "fix": "345", "desc": "327"}, {"messageId": "322", "fix": "346", "desc": "324"}, {"messageId": "325", "fix": "347", "desc": "327"}, {"desc": "348", "fix": "349"}, {"messageId": "322", "fix": "350", "desc": "324"}, {"messageId": "325", "fix": "351", "desc": "327"}, {"messageId": "322", "fix": "352", "desc": "324"}, {"messageId": "325", "fix": "353", "desc": "327"}, {"messageId": "322", "fix": "354", "desc": "324"}, {"messageId": "325", "fix": "355", "desc": "327"}, {"messageId": "322", "fix": "356", "desc": "324"}, {"messageId": "325", "fix": "357", "desc": "327"}, {"messageId": "322", "fix": "358", "desc": "324"}, {"messageId": "325", "fix": "359", "desc": "327"}, {"messageId": "322", "fix": "360", "desc": "324"}, {"messageId": "325", "fix": "361", "desc": "327"}, {"messageId": "322", "fix": "362", "desc": "324"}, {"messageId": "325", "fix": "363", "desc": "327"}, {"messageId": "322", "fix": "364", "desc": "324"}, {"messageId": "325", "fix": "365", "desc": "327"}, {"messageId": "322", "fix": "366", "desc": "324"}, {"messageId": "325", "fix": "367", "desc": "327"}, {"messageId": "322", "fix": "368", "desc": "324"}, {"messageId": "325", "fix": "369", "desc": "327"}, {"messageId": "322", "fix": "370", "desc": "324"}, {"messageId": "325", "fix": "371", "desc": "327"}, {"messageId": "322", "fix": "372", "desc": "324"}, {"messageId": "325", "fix": "373", "desc": "327"}, {"messageId": "322", "fix": "374", "desc": "324"}, {"messageId": "325", "fix": "375", "desc": "327"}, {"messageId": "322", "fix": "376", "desc": "324"}, {"messageId": "325", "fix": "377", "desc": "327"}, {"messageId": "322", "fix": "378", "desc": "324"}, {"messageId": "325", "fix": "379", "desc": "327"}, {"messageId": "322", "fix": "380", "desc": "324"}, {"messageId": "325", "fix": "381", "desc": "327"}, {"messageId": "322", "fix": "382", "desc": "324"}, {"messageId": "325", "fix": "383", "desc": "327"}, {"messageId": "322", "fix": "384", "desc": "324"}, {"messageId": "325", "fix": "385", "desc": "327"}, {"messageId": "322", "fix": "386", "desc": "324"}, {"messageId": "325", "fix": "387", "desc": "327"}, {"messageId": "322", "fix": "388", "desc": "324"}, {"messageId": "325", "fix": "389", "desc": "327"}, {"messageId": "322", "fix": "390", "desc": "324"}, {"messageId": "325", "fix": "391", "desc": "327"}, {"messageId": "322", "fix": "392", "desc": "324"}, {"messageId": "325", "fix": "393", "desc": "327"}, {"messageId": "322", "fix": "394", "desc": "324"}, {"messageId": "325", "fix": "395", "desc": "327"}, {"messageId": "322", "fix": "396", "desc": "324"}, {"messageId": "325", "fix": "397", "desc": "327"}, {"messageId": "322", "fix": "398", "desc": "324"}, {"messageId": "325", "fix": "399", "desc": "327"}, {"messageId": "322", "fix": "400", "desc": "324"}, {"messageId": "325", "fix": "401", "desc": "327"}, "suggestUnknown", {"range": "402", "text": "403"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "404", "text": "405"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [fetchCustomers, searchTerm, statusFilter]", {"range": "406", "text": "407"}, {"range": "408", "text": "403"}, {"range": "409", "text": "405"}, {"range": "410", "text": "403"}, {"range": "411", "text": "405"}, {"range": "412", "text": "403"}, {"range": "413", "text": "405"}, {"range": "414", "text": "403"}, {"range": "415", "text": "405"}, {"range": "416", "text": "403"}, {"range": "417", "text": "405"}, "Update the dependencies array to be: [fetchOrders, searchTerm, statusFilter]", {"range": "418", "text": "419"}, {"range": "420", "text": "403"}, {"range": "421", "text": "405"}, {"range": "422", "text": "403"}, {"range": "423", "text": "405"}, {"range": "424", "text": "403"}, {"range": "425", "text": "405"}, "Update the dependencies array to be: [fetchProviders, searchTerm, statusFilter]", {"range": "426", "text": "427"}, {"range": "428", "text": "403"}, {"range": "429", "text": "405"}, {"range": "430", "text": "403"}, {"range": "431", "text": "405"}, {"range": "432", "text": "403"}, {"range": "433", "text": "405"}, {"range": "434", "text": "403"}, {"range": "435", "text": "405"}, {"range": "436", "text": "403"}, {"range": "437", "text": "405"}, {"range": "438", "text": "403"}, {"range": "439", "text": "405"}, {"range": "440", "text": "403"}, {"range": "441", "text": "405"}, {"range": "442", "text": "403"}, {"range": "443", "text": "405"}, {"range": "444", "text": "403"}, {"range": "445", "text": "405"}, {"range": "446", "text": "403"}, {"range": "447", "text": "405"}, {"range": "448", "text": "403"}, {"range": "449", "text": "405"}, {"range": "450", "text": "403"}, {"range": "451", "text": "405"}, {"range": "452", "text": "403"}, {"range": "453", "text": "405"}, {"range": "454", "text": "403"}, {"range": "455", "text": "405"}, {"range": "456", "text": "403"}, {"range": "457", "text": "405"}, {"range": "458", "text": "403"}, {"range": "459", "text": "405"}, {"range": "460", "text": "403"}, {"range": "461", "text": "405"}, {"range": "462", "text": "403"}, {"range": "463", "text": "405"}, {"range": "464", "text": "403"}, {"range": "465", "text": "405"}, {"range": "466", "text": "403"}, {"range": "467", "text": "405"}, {"range": "468", "text": "403"}, {"range": "469", "text": "405"}, {"range": "470", "text": "403"}, {"range": "471", "text": "405"}, {"range": "472", "text": "403"}, {"range": "473", "text": "405"}, {"range": "474", "text": "403"}, {"range": "475", "text": "405"}, {"range": "476", "text": "403"}, {"range": "477", "text": "405"}, {"range": "478", "text": "403"}, {"range": "479", "text": "405"}, [1135, 1138], "unknown", [1135, 1138], "never", [1165, 1191], "[fetchCustomers, searchTerm, statusFilter]", [1303, 1306], [1303, 1306], [1836, 1839], [1836, 1839], [2234, 2237], [2234, 2237], [1452, 1455], [1452, 1455], [1499, 1502], [1499, 1502], [1131, 1157], "[fetchOrders, searchTerm, statusFilter]", [1266, 1269], [1266, 1269], [1747, 1750], [1747, 1750], [2148, 2151], [2148, 2151], [1209, 1235], "[fetchProviders, searchTerm, statusFilter]", [1347, 1350], [1347, 1350], [1853, 1856], [1853, 1856], [2305, 2308], [2305, 2308], [2721, 2724], [2721, 2724], [4127, 4130], [4127, 4130], [5181, 5184], [5181, 5184], [5955, 5958], [5955, 5958], [6848, 6851], [6848, 6851], [7164, 7167], [7164, 7167], [7341, 7344], [7341, 7344], [7732, 7735], [7732, 7735], [8058, 8061], [8058, 8061], [8240, 8243], [8240, 8243], [8640, 8643], [8640, 8643], [9581, 9584], [9581, 9584], [9776, 9779], [9776, 9779], [9951, 9954], [9951, 9954], [10363, 10366], [10363, 10366], [10620, 10623], [10620, 10623], [10908, 10911], [10908, 10911], [11071, 11074], [11071, 11074], [4538, 4541], [4538, 4541], [4548, 4551], [4548, 4551], [2419, 2422], [2419, 2422], [3310, 3313], [3310, 3313], [3898, 3901], [3898, 3901]]