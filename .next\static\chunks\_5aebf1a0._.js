(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyticsApi": (()=>analyticsApi),
    "apiRequest": (()=>apiRequest),
    "authApi": (()=>authApi),
    "categoriesApi": (()=>categoriesApi),
    "clearTokens": (()=>clearTokens),
    "couponsApi": (()=>couponsApi),
    "getTokens": (()=>getTokens),
    "ordersApi": (()=>ordersApi),
    "paymentsApi": (()=>paymentsApi),
    "providersApi": (()=>providersApi),
    "schedulingApi": (()=>schedulingApi),
    "servicesApi": (()=>servicesApi),
    "setTokens": (()=>setTokens),
    "usersApi": (()=>usersApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:8000/api") || 'http://localhost:8000/api';
const getTokens = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const access = localStorage.getItem('access_token') || '';
    const refresh = localStorage.getItem('refresh_token') || '';
    return {
        access,
        refresh
    };
};
const setTokens = (tokens)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    localStorage.setItem('access_token', tokens.access);
    localStorage.setItem('refresh_token', tokens.refresh);
};
const clearTokens = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
};
// Token refresh function
const refreshToken = async ()=>{
    const { refresh } = getTokens();
    if (!refresh) return null;
    try {
        const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                refresh
            })
        });
        if (response.ok) {
            const data = await response.json();
            setTokens({
                access: data.access,
                refresh
            });
            return data.access;
        } else {
            clearTokens();
            return null;
        }
    } catch (error) {
        console.error('Token refresh failed:', error);
        clearTokens();
        return null;
    }
};
const apiRequest = async (endpoint, options = {})=>{
    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
    const { access } = getTokens();
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    if (access) {
        headers.Authorization = `Bearer ${access}`;
    }
    console.log(`API Request: ${options.method || 'GET'} ${url}`);
    if (options.body) {
        console.log('Request body:', options.body);
    }
    let response = await fetch(url, {
        ...options,
        headers
    });
    // If token expired, try to refresh and retry
    if (response.status === 401 && access) {
        console.log('Token expired, attempting refresh...');
        const newToken = await refreshToken();
        if (newToken) {
            headers.Authorization = `Bearer ${newToken}`;
            response = await fetch(url, {
                ...options,
                headers
            });
        } else {
            // Redirect to login if refresh fails
            if ("TURBOPACK compile-time truthy", 1) {
                window.location.href = '/auth/login';
            }
            throw new Error('Authentication failed');
        }
    }
    const data = await response.json();
    console.log(`API Response: ${response.status}`, data);
    if (!response.ok) {
        const error = {
            message: data.message || data.error || 'An error occurred',
            details: data.details || data,
            status: response.status
        };
        throw error;
    }
    return data;
};
const authApi = {
    // Staff login with email/password
    loginStaff: (credentials)=>apiRequest('/auth/login/email/', {
            method: 'POST',
            body: JSON.stringify(credentials)
        }),
    // Get user profile
    getProfile: ()=>apiRequest('/auth/profile/'),
    // Update user profile
    updateProfile: (data)=>apiRequest('/auth/profile/', {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Change password
    changePassword: (data)=>apiRequest('/auth/change-password/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Logout
    logout: ()=>apiRequest('/auth/logout/', {
            method: 'POST'
        })
};
const ordersApi = {
    // Get all orders (staff can see all)
    getOrders: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/orders/${query}`);
    },
    // Get order details
    getOrderDetail: (orderNumber)=>apiRequest(`/orders/${orderNumber}/`),
    // Update order status
    updateOrderStatus: (orderNumber, data)=>apiRequest(`/orders/${orderNumber}/status/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Assign provider to order
    assignProvider: (orderNumber, data)=>apiRequest(`/orders/${orderNumber}/assign-provider/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Cancel order
    cancelOrder: (orderNumber, data)=>apiRequest(`/orders/${orderNumber}/cancel/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const usersApi = {
    // Get all users
    getUsers: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/auth/users/${query}`);
    },
    // Get user details
    getUserDetail: (userId)=>apiRequest(`/auth/users/${userId}/`),
    // Update user
    updateUser: (userId, data)=>apiRequest(`/auth/users/${userId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Lock/unlock user
    toggleUserLock: (userId, data)=>apiRequest(`/auth/users/${userId}/toggle-lock/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const providersApi = {
    // Get all providers
    getProviders: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/providers/${query}`);
    },
    // Get provider details
    getProviderDetail: (providerId)=>apiRequest(`/providers/${providerId}/`),
    // Update provider verification status
    updateProviderVerification: (providerId, data)=>apiRequest(`/providers/${providerId}/verification/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Toggle provider availability
    toggleProviderAvailability: (providerId, data)=>apiRequest(`/providers/${providerId}/availability/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const servicesApi = {
    // Get all services
    getServices: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/catalogue/services/${query}`);
    },
    // Get service details
    getServiceDetail: (serviceId)=>apiRequest(`/catalogue/services/${serviceId}/`),
    // Create service
    createService: (data)=>apiRequest('/catalogue/services/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update service
    updateService: (serviceId, data)=>apiRequest(`/catalogue/services/${serviceId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Delete service
    deleteService: (serviceId)=>apiRequest(`/catalogue/services/${serviceId}/`, {
            method: 'DELETE'
        })
};
const categoriesApi = {
    // Get all categories
    getCategories: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/catalogue/categories/${query}`);
    },
    // Get category details
    getCategoryDetail: (categoryId)=>apiRequest(`/catalogue/categories/${categoryId}/`),
    // Create category
    createCategory: (data)=>apiRequest('/catalogue/categories/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update category
    updateCategory: (categoryId, data)=>apiRequest(`/catalogue/categories/${categoryId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Delete category
    deleteCategory: (categoryId)=>apiRequest(`/catalogue/categories/${categoryId}/`, {
            method: 'DELETE'
        })
};
const paymentsApi = {
    // Get all transactions
    getTransactions: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/payments/transactions/${query}`);
    },
    // Get transaction details
    getTransactionDetail: (transactionId)=>apiRequest(`/payments/transactions/${transactionId}/`),
    // Process refund
    processRefund: (transactionId, data)=>apiRequest(`/payments/transactions/${transactionId}/refund/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
const analyticsApi = {
    // Get dashboard stats (using orders dashboard endpoint)
    getDashboardStats: ()=>apiRequest('/orders/dashboard/'),
    // Get user statistics (using auth admin endpoint)
    getUserStats: ()=>apiRequest('/auth/admin/user-stats/')
};
const schedulingApi = {
    // Get time slots
    getTimeSlots: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/scheduling/slots/${query}`);
    },
    // Create time slot
    createTimeSlot: (data)=>apiRequest('/scheduling/slots/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update time slot
    updateTimeSlot: (slotId, data)=>apiRequest(`/scheduling/slots/${slotId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Block time slot
    blockTimeSlot: (slotId, data)=>apiRequest(`/scheduling/slots/${slotId}/block/`, {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Get slot bookings
    getSlotBookings: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/scheduling/bookings/${query}`);
    }
};
const couponsApi = {
    // Get all coupons
    getCoupons: (params)=>{
        const query = params ? `?${new URLSearchParams(params).toString()}` : '';
        return apiRequest(`/coupons/${query}`);
    },
    // Get coupon details
    getCouponDetail: (couponId)=>apiRequest(`/coupons/${couponId}/`),
    // Create coupon
    createCoupon: (data)=>apiRequest('/coupons/', {
            method: 'POST',
            body: JSON.stringify(data)
        }),
    // Update coupon
    updateCoupon: (couponId, data)=>apiRequest(`/coupons/${couponId}/`, {
            method: 'PUT',
            body: JSON.stringify(data)
        }),
    // Delete coupon
    deleteCoupon: (couponId)=>apiRequest(`/coupons/${couponId}/`, {
            method: 'DELETE'
        }),
    // Toggle coupon status
    toggleCouponStatus: (couponId, data)=>apiRequest(`/coupons/${couponId}/toggle/`, {
            method: 'POST',
            body: JSON.stringify(data)
        })
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const AuthProvider = ({ children })=>{
    _s1();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const isAuthenticated = !!user;
    // Initialize auth state on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const initializeAuth = {
                "AuthProvider.useEffect.initializeAuth": async ()=>{
                    const { refresh } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokens"])();
                    if (refresh) {
                        try {
                            // Try to get user profile to verify token validity
                            const userProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].getProfile();
                            // Ensure user is staff
                            if (userProfile.user_type !== 'STAFF') {
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearTokens"])();
                                throw new Error('Access denied: Staff access required');
                            }
                            setUser(userProfile);
                        } catch (error) {
                            // Token is invalid, clear it
                            console.error('Auth initialization failed:', error);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearTokens"])();
                        }
                    }
                    setIsLoading(false);
                }
            }["AuthProvider.useEffect.initializeAuth"];
            initializeAuth();
        }
    }["AuthProvider.useEffect"], []);
    const login = async (credentials)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].loginStaff(credentials);
            // Ensure user is staff
            if (response.user.user_type !== 'STAFF') {
                throw new Error('Access denied: Staff access required');
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setTokens"])(response.tokens);
            setUser(response.user);
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    };
    const logout = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].logout();
        } catch (error) {
            // Even if logout fails on server, clear local tokens
            console.error('Logout error:', error);
        } finally{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearTokens"])();
            setUser(null);
        }
    };
    const updateUser = (userData)=>{
        if (user) {
            setUser({
                ...user,
                ...userData
            });
        }
    };
    const refreshUserProfile = async ()=>{
        if (isAuthenticated) {
            try {
                const userProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authApi"].getProfile();
                setUser(userProfile);
            } catch (error) {
                console.error('Failed to refresh user profile:', error);
            }
        }
    };
    const value = {
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        updateUser,
        refreshUserProfile
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 124,
        columnNumber: 5
    }, this);
};
_s1(AuthProvider, "YajQB7LURzRD+QP5gw0+K2TZIWA=");
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_5aebf1a0._.js.map