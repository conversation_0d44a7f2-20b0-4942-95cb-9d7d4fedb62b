'use client';

import React, { useEffect, useState } from 'react';
import { 
  Search, 
  Filter, 
  Eye, 
  Edit,
  MoreHorizontal,
  Plus,
  User,
  Phone,
  Mail,
  Star,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { StatusBadge } from '@/components/ui/Badge';
import { LoadingCard } from '@/components/ui/LoadingSpinner';
import { providersApi } from '@/lib/api';
import { formatDateTime, formatRelativeTime, formatRating } from '@/lib/utils';
import { Provider, PaginatedResponse } from '@/types/api';

export default function ProvidersPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [pagination, setPagination] = useState({
    count: 0,
    next: null,
    previous: null,
  });

  useEffect(() => {
    fetchProviders();
  }, [searchTerm, statusFilter]);

  const fetchProviders = async () => {
    try {
      setLoading(true);
      const params: Record<string, any> = {
        ordering: '-created_at',
        limit: 20,
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter) {
        params.verification_status = statusFilter;
      }

      const response = await providersApi.getProviders(params) as PaginatedResponse<Provider>;
      setProviders(response.results);
      setPagination({
        count: response.count,
        next: response.next,
        previous: response.previous,
      });
    } catch (err: any) {
      setError(err.message || 'Failed to load providers');
      console.error('Providers fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationUpdate = async (providerId: number, status: string) => {
    try {
      await providersApi.updateProviderVerification(providerId, { 
        verification_status: status 
      });
      // Refresh providers list
      fetchProviders();
    } catch (err: any) {
      console.error('Verification update error:', err);
      alert('Failed to update provider verification');
    }
  };

  const handleAvailabilityToggle = async (providerId: number, isAvailable: boolean) => {
    try {
      await providersApi.toggleProviderAvailability(providerId, { 
        is_available: !isAvailable 
      });
      // Refresh providers list
      fetchProviders();
    } catch (err: any) {
      console.error('Availability update error:', err);
      alert('Failed to update provider availability');
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <h1 className="text-2xl font-bold text-gray-900">Service Providers</h1>
          <LoadingCard />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Service Providers</h1>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add Provider
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search providers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="verified">Verified</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Providers List */}
        <Card>
          <CardHeader>
            <CardTitle>
              Service Providers ({pagination.count})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {error ? (
              <div className="text-center py-8 text-red-600">
                {error}
              </div>
            ) : providers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No providers found
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Provider</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Contact</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Verification</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Rating</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Orders</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {providers.map((provider) => (
                      <tr key={provider.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                              <User className="w-5 h-5 text-gray-600" />
                            </div>
                            <div className="ml-3">
                              <div className="font-medium text-gray-900">{provider.user_name}</div>
                              <div className="text-sm text-gray-500">
                                {provider.business_name || 'No business name'}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="space-y-1">
                            {provider.user_email && (
                              <div className="flex items-center text-sm text-gray-600">
                                <Mail className="w-4 h-4 mr-2" />
                                {provider.user_email}
                              </div>
                            )}
                            <div className="flex items-center text-sm text-gray-600">
                              <Phone className="w-4 h-4 mr-2" />
                              {provider.user_mobile}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <StatusBadge status={provider.verification_status} />
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-500 mr-1" />
                            <span className="font-medium">{formatRating(provider.rating)}</span>
                            <span className="text-sm text-gray-500 ml-1">
                              ({provider.total_reviews})
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm">
                            <div className="font-medium">{provider.total_orders_completed}</div>
                            <div className="text-gray-500">completed</div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <StatusBadge status={provider.is_available ? 'available' : 'unavailable'} />
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                            
                            {provider.verification_status === 'pending' && (
                              <>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  onClick={() => handleVerificationUpdate(provider.id, 'verified')}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  onClick={() => handleVerificationUpdate(provider.id, 'rejected')}
                                  className="text-red-600"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                            
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleAvailabilityToggle(provider.id, provider.is_available)}
                            >
                              {provider.is_available ? 'Disable' : 'Enable'}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.count > 20 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {providers.length} of {pagination.count} providers
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                disabled={!pagination.previous}
              >
                Previous
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                disabled={!pagination.next}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
