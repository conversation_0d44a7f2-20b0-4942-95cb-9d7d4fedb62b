(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{646:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},858:(e,s,r)=>{"use strict";r.d(s,{N:()=>_});var t=r(5155),a=r(2115),l=r(6874),n=r.n(l),i=r(5695),c=r(7340),d=r(6151),x=r(7580),o=r(5670),m=r(7108),h=r(3332),g=r(1586),u=r(9074),j=r(2713),y=r(381),f=r(4416),p=r(1007),b=r(4835),v=r(4783),N=r(9434),w=r(283),k=r(3741);let A=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Orders",href:"/orders",icon:d.A},{name:"Customers",href:"/customers",icon:x.A},{name:"Providers",href:"/providers",icon:o.A},{name:"Services",href:"/services",icon:m.A},{name:"Categories",href:"/categories",icon:h.A},{name:"Payments",href:"/payments",icon:g.A},{name:"Scheduling",href:"/scheduling",icon:u.A},{name:"Analytics",href:"/analytics",icon:j.A},{name:"Settings",href:"/settings",icon:y.A}],_=e=>{let{children:s}=e,[r,l]=(0,a.useState)(!1),c=(0,i.usePathname)(),{user:d,logout:x}=(0,w.A)(),o=async()=>{try{await x()}catch(e){console.error("Logout failed:",e)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r&&(0,t.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>l(!1)}),(0,t.jsx)("div",{className:(0,N.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",r?"translate-x-0":"-translate-x-full"),children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Staff Dashboard"}),(0,t.jsx)("button",{onClick:()=>l(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(f.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:A.map(e=>{let s=c===e.href||c.startsWith(e.href+"/");return(0,t.jsxs)(n(),{href:e.href,className:(0,N.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>l(!1),children:[(0,t.jsx)(e.icon,{className:"w-5 h-5 mr-3"}),e.name]},e.name)})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(p.A,{className:"w-4 h-4 text-blue-600"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:null==d?void 0:d.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:null==d?void 0:d.email})]})]}),(0,t.jsxs)(k.$,{variant:"outline",size:"sm",fullWidth:!0,onClick:o,className:"text-red-600 border-red-200 hover:bg-red-50",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,t.jsx)("button",{onClick:()=>l(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(v.A,{className:"w-5 h-5"})}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",null==d?void 0:d.name]})})]})}),(0,t.jsx)("main",{className:"p-6",children:s})]})]})}},2814:(e,s,r)=>{"use strict";r.d(s,{E:()=>l,W:()=>n});var t=r(5155);r(2115);var a=r(9434);let l=e=>{let{children:s,variant:r="default",size:l="md",className:n}=e;return(0,t.jsx)("span",{className:(0,a.cn)("inline-flex items-center font-medium rounded-full",{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800"}[r],{sm:"px-2 py-1 text-xs",md:"px-2.5 py-0.5 text-sm",lg:"px-3 py-1 text-base"}[l],n),children:s})},n=e=>{let{status:s,className:r}=e;return(0,t.jsx)(l,{variant:{pending:"warning",confirmed:"info",assigned:"info",in_progress:"info",completed:"success",cancelled:"error",refunded:"default",paid:"success",failed:"error",active:"success",inactive:"default",locked:"error",verified:"success",rejected:"error",available:"success",unavailable:"error",blocked:"error"}[s.toLowerCase()]||"default",className:r,children:s.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ")})}},3741:(e,s,r)=>{"use strict";r.d(s,{$:()=>n});var t=r(5155);r(2115);var a=r(9434),l=r(4338);let n=e=>{let{children:s,variant:r="primary",size:n="md",loading:i=!1,disabled:c=!1,className:d,onClick:x,type:o="button",fullWidth:m=!1}=e;return(0,t.jsxs)("button",{type:o,onClick:x,disabled:c||i,className:(0,a.cn)("inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],m&&"w-full",d),children:[i&&(0,t.jsx)(l.kt,{size:"sm",className:"mr-2"}),s]})}},5504:(e,s,r)=>{Promise.resolve().then(r.bind(r,9686))},7703:(e,s,r)=>{"use strict";r.d(s,{CN:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>n});var t=r(5155);r(2115);var a=r(9434);let l=e=>{let{children:s,className:r,padding:l="md"}=e;return(0,t.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow border border-gray-200",{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[l],r),children:s})},n=e=>{let{children:s,className:r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("border-b border-gray-200 pb-4 mb-4",r),children:s})},i=e=>{let{children:s,className:r}=e;return(0,t.jsx)("h3",{className:(0,a.cn)("text-lg font-semibold text-gray-900",r),children:s})},c=e=>{let{children:s,className:r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("text-gray-600",r),children:s})},d=e=>{let{title:s,value:r,change:n,icon:i,className:c}=e;return(0,t.jsx)(l,{className:(0,a.cn)("",c),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:s}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:r}),n&&(0,t.jsxs)("p",{className:(0,a.cn)("text-sm font-medium","increase"===n.type?"text-green-600":"text-red-600"),children:["increase"===n.type?"↗":"↘"," ",n.value]})]}),i&&(0,t.jsx)("div",{className:"text-gray-400",children:i})]})})}},9686:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>y});var t=r(5155),a=r(2115),l=r(9946);let n=(0,l.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var i=r(6151),c=r(646);let d=(0,l.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),x=(0,l.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var o=r(858),m=r(7703),h=r(4338),g=r(2814),u=r(5731),j=r(9434);function y(){let[e,s]=(0,a.useState)(null),[r,l]=(0,a.useState)([]),[y,f]=(0,a.useState)(!0),[p,b]=(0,a.useState)("");return((0,a.useEffect)(()=>{(async()=>{try{f(!0);let e=await u.LP.getDashboardStats();s(e),l(e.recent_orders||[])}catch(e){b(e.message||"Failed to load dashboard data"),console.error("Dashboard data fetch error:",e)}finally{f(!1)}})()},[]),y)?(0,t.jsx)(o.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,s)=>(0,t.jsx)(h.B0,{},s))}),(0,t.jsx)(h.B0,{})]})}):p?(0,t.jsx)(o.N,{children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(n,{className:"mx-auto h-12 w-12 text-red-400"}),(0,t.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Error loading dashboard"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:p})]})}):(0,t.jsx)(o.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(m.CN,{title:"Total Orders",value:(null==e?void 0:e.total_orders)||0,icon:(0,t.jsx)(i.A,{className:"w-6 h-6"}),change:{value:"".concat((null==e?void 0:e.pending_orders)||0," pending"),type:"increase"}}),(0,t.jsx)(m.CN,{title:"Confirmed Orders",value:(null==e?void 0:e.confirmed_orders)||0,icon:(0,t.jsx)(c.A,{className:"w-6 h-6"})}),(0,t.jsx)(m.CN,{title:"In Progress",value:(null==e?void 0:e.in_progress_orders)||0,icon:(0,t.jsx)(d,{className:"w-6 h-6"})}),(0,t.jsx)(m.CN,{title:"Completed Orders",value:(null==e?void 0:e.completed_orders)||0,icon:(0,t.jsx)(x,{className:"w-6 h-6"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsxs)(m.ZB,{className:"flex items-center",children:[(0,t.jsx)(d,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Pending Orders"]})}),(0,t.jsxs)(m.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:(null==e?void 0:e.pending_orders)||0}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Awaiting processing"})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsxs)(m.ZB,{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 mr-2 text-green-500"}),"Cancelled Orders"]})}),(0,t.jsxs)(m.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:(null==e?void 0:e.cancelled_orders)||0}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Cancelled orders"})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsxs)(m.ZB,{className:"flex items-center",children:[(0,t.jsx)(x,{className:"w-5 h-5 mr-2 text-blue-500"}),"Order Status Overview"]})}),(0,t.jsx)(m.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Total:"}),(0,t.jsx)("span",{className:"font-medium",children:(null==e?void 0:e.total_orders)||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"In Progress:"}),(0,t.jsx)("span",{className:"font-medium",children:(null==e?void 0:e.in_progress_orders)||0})]})]})})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Recent Orders"})}),(0,t.jsx)(m.Wu,{children:0===r.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent orders"}):(0,t.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("span",{className:"font-medium text-gray-900",children:["#",e.order_number]}),(0,t.jsx)(g.E,{variant:"info",size:"sm",children:e.status})]}),(0,t.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:[e.customer_name," • ",e.items_count," item",1!==e.items_count?"s":""]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:(0,j.vv)(e.total_amount)}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:(0,j.fw)(e.created_at)})]})]},e.id))})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[743,952,441,684,358],()=>s(5504)),_N_E=e.O()}]);