{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/src/lib/api.ts"], "sourcesContent": ["import { AuthTokens, LoginResponse, User, ApiError } from '@/types/api';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';\n\n// Token management\nexport const getTokens = (): AuthTokens => {\n  if (typeof window === 'undefined') return { access: '', refresh: '' };\n  \n  const access = localStorage.getItem('access_token') || '';\n  const refresh = localStorage.getItem('refresh_token') || '';\n  return { access, refresh };\n};\n\nexport const setTokens = (tokens: AuthTokens): void => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.setItem('access_token', tokens.access);\n  localStorage.setItem('refresh_token', tokens.refresh);\n};\n\nexport const clearTokens = (): void => {\n  if (typeof window === 'undefined') return;\n  \n  localStorage.removeItem('access_token');\n  localStorage.removeItem('refresh_token');\n};\n\n// Token refresh function\nconst refreshToken = async (): Promise<string | null> => {\n  const { refresh } = getTokens();\n  if (!refresh) return null;\n\n  try {\n    const response = await fetch(`${API_BASE_URL}/auth/token/refresh/`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ refresh }),\n    });\n\n    if (response.ok) {\n      const data = await response.json();\n      setTokens({ access: data.access, refresh });\n      return data.access;\n    } else {\n      clearTokens();\n      return null;\n    }\n  } catch (error) {\n    console.error('Token refresh failed:', error);\n    clearTokens();\n    return null;\n  }\n};\n\n// API request wrapper with automatic token refresh\nexport const apiRequest = async <T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;\n\n  const { access } = getTokens();\n  const headers: Record<string, string> = {\n    'Content-Type': 'application/json',\n    ...(options.headers as Record<string, string>),\n  };\n\n  if (access) {\n    headers.Authorization = `Bearer ${access}`;\n  }\n\n  console.log(`API Request: ${options.method || 'GET'} ${url}`);\n  if (options.body) {\n    console.log('Request body:', options.body);\n  }\n\n  let response = await fetch(url, {\n    ...options,\n    headers,\n  });\n\n  // If token expired, try to refresh and retry\n  if (response.status === 401 && access) {\n    console.log('Token expired, attempting refresh...');\n    const newToken = await refreshToken();\n    \n    if (newToken) {\n      headers.Authorization = `Bearer ${newToken}`;\n      response = await fetch(url, {\n        ...options,\n        headers,\n      });\n    } else {\n      // Redirect to login if refresh fails\n      if (typeof window !== 'undefined') {\n        window.location.href = '/auth/login';\n      }\n      throw new Error('Authentication failed');\n    }\n  }\n\n  const data = await response.json();\n  console.log(`API Response: ${response.status}`, data);\n\n  if (!response.ok) {\n    const error: ApiError = {\n      message: data.message || data.error || 'An error occurred',\n      details: data.details || data,\n      status: response.status,\n    };\n    throw error;\n  }\n\n  return data;\n};\n\n// Authentication API\nexport const authApi = {\n  // Staff login with email/password\n  loginStaff: (credentials: { email: string; password: string }) =>\n    apiRequest<LoginResponse>('/auth/login/email/', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    }),\n\n  // Get user profile\n  getProfile: () =>\n    apiRequest<User>('/auth/profile/'),\n\n  // Update user profile\n  updateProfile: (data: Partial<User>) =>\n    apiRequest<User>('/auth/profile/', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Change password\n  changePassword: (data: { old_password: string; new_password: string; confirm_password: string }) =>\n    apiRequest('/auth/change-password/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Logout\n  logout: () =>\n    apiRequest('/auth/logout/', {\n      method: 'POST',\n    }),\n};\n\n// Orders API\nexport const ordersApi = {\n  // Get all orders (staff can see all)\n  getOrders: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/orders/${query}`);\n  },\n\n  // Get order details\n  getOrderDetail: (orderNumber: string) =>\n    apiRequest(`/orders/${orderNumber}/`),\n\n  // Update order status\n  updateOrderStatus: (orderNumber: string, data: { status: string; admin_notes?: string }) =>\n    apiRequest(`/orders/${orderNumber}/status/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Assign provider to order\n  assignProvider: (orderNumber: string, data: { provider_id: number; admin_notes?: string }) =>\n    apiRequest(`/orders/${orderNumber}/assign-provider/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Cancel order\n  cancelOrder: (orderNumber: string, data: { reason: string; admin_notes?: string }) =>\n    apiRequest(`/orders/${orderNumber}/cancel/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Users API\nexport const usersApi = {\n  // Get all users\n  getUsers: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/auth/users/${query}`);\n  },\n\n  // Get user details\n  getUserDetail: (userId: number) =>\n    apiRequest(`/auth/users/${userId}/`),\n\n  // Update user\n  updateUser: (userId: number, data: Partial<User>) =>\n    apiRequest(`/auth/users/${userId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Lock/unlock user\n  toggleUserLock: (userId: number, data: { is_locked: boolean; lockout_duration?: number }) =>\n    apiRequest(`/auth/users/${userId}/toggle-lock/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Providers API\nexport const providersApi = {\n  // Get all providers\n  getProviders: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/providers/${query}`);\n  },\n\n  // Get provider details\n  getProviderDetail: (providerId: number) =>\n    apiRequest(`/providers/${providerId}/`),\n\n  // Update provider verification status\n  updateProviderVerification: (providerId: number, data: { verification_status: string; admin_notes?: string }) =>\n    apiRequest(`/providers/${providerId}/verification/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Toggle provider availability\n  toggleProviderAvailability: (providerId: number, data: { is_available: boolean }) =>\n    apiRequest(`/providers/${providerId}/availability/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Services API\nexport const servicesApi = {\n  // Get all services\n  getServices: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/catalogue/services/${query}`);\n  },\n\n  // Get service details\n  getServiceDetail: (serviceId: number) =>\n    apiRequest(`/catalogue/services/${serviceId}/`),\n\n  // Create service\n  createService: (data: any) =>\n    apiRequest('/catalogue/services/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update service\n  updateService: (serviceId: number, data: any) =>\n    apiRequest(`/catalogue/services/${serviceId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete service\n  deleteService: (serviceId: number) =>\n    apiRequest(`/catalogue/services/${serviceId}/`, {\n      method: 'DELETE',\n    }),\n};\n\n// Categories API\nexport const categoriesApi = {\n  // Get all categories\n  getCategories: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/catalogue/categories/${query}`);\n  },\n\n  // Get category details\n  getCategoryDetail: (categoryId: number) =>\n    apiRequest(`/catalogue/categories/${categoryId}/`),\n\n  // Create category\n  createCategory: (data: any) =>\n    apiRequest('/catalogue/categories/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update category\n  updateCategory: (categoryId: number, data: any) =>\n    apiRequest(`/catalogue/categories/${categoryId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete category\n  deleteCategory: (categoryId: number) =>\n    apiRequest(`/catalogue/categories/${categoryId}/`, {\n      method: 'DELETE',\n    }),\n};\n\n// Payments API\nexport const paymentsApi = {\n  // Get all transactions\n  getTransactions: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/payments/transactions/${query}`);\n  },\n\n  // Get transaction details\n  getTransactionDetail: (transactionId: string) =>\n    apiRequest(`/payments/transactions/${transactionId}/`),\n\n  // Process refund\n  processRefund: (transactionId: string, data: { amount?: string; reason: string }) =>\n    apiRequest(`/payments/transactions/${transactionId}/refund/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Analytics API\nexport const analyticsApi = {\n  // Get dashboard stats (using orders dashboard endpoint)\n  getDashboardStats: () =>\n    apiRequest('/orders/dashboard/'),\n\n  // Get user statistics (using auth admin endpoint)\n  getUserStats: () =>\n    apiRequest('/auth/admin/user-stats/'),\n};\n\n// Scheduling API\nexport const schedulingApi = {\n  // Get time slots\n  getTimeSlots: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/scheduling/slots/${query}`);\n  },\n\n  // Create time slot\n  createTimeSlot: (data: any) =>\n    apiRequest('/scheduling/slots/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update time slot\n  updateTimeSlot: (slotId: number, data: any) =>\n    apiRequest(`/scheduling/slots/${slotId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Block time slot\n  blockTimeSlot: (slotId: number, data: { reason: string; blocked_by: string }) =>\n    apiRequest(`/scheduling/slots/${slotId}/block/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Get slot bookings\n  getSlotBookings: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/scheduling/bookings/${query}`);\n  },\n};\n\n// Coupons API\nexport const couponsApi = {\n  // Get all coupons\n  getCoupons: (params?: Record<string, any>) => {\n    const query = params ? `?${new URLSearchParams(params).toString()}` : '';\n    return apiRequest(`/coupons/${query}`);\n  },\n\n  // Get coupon details\n  getCouponDetail: (couponId: number) =>\n    apiRequest(`/coupons/${couponId}/`),\n\n  // Create coupon\n  createCoupon: (data: any) =>\n    apiRequest('/coupons/', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update coupon\n  updateCoupon: (couponId: number, data: any) =>\n    apiRequest(`/coupons/${couponId}/`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete coupon\n  deleteCoupon: (couponId: number) =>\n    apiRequest(`/coupons/${couponId}/`, {\n      method: 'DELETE',\n    }),\n\n  // Toggle coupon status\n  toggleCouponStatus: (couponId: number, data: { is_active: boolean }) =>\n    apiRequest(`/coupons/${couponId}/toggle/`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA,MAAM,eAAe,iEAAwC;AAGtD,MAAM,YAAY;IACvB,wCAAmC,OAAO;QAAE,QAAQ;QAAI,SAAS;IAAG;;IAEpE,MAAM;IACN,MAAM;AAER;AAEO,MAAM,YAAY,CAAC;IACxB,wCAAmC;;AAIrC;AAEO,MAAM,cAAc;IACzB,wCAAmC;;AAIrC;AAEA,yBAAyB;AACzB,MAAM,eAAe;IACnB,MAAM,EAAE,OAAO,EAAE,GAAG;IACpB,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;gBAAE,QAAQ,KAAK,MAAM;gBAAE;YAAQ;YACzC,OAAO,KAAK,MAAM;QACpB,OAAO;YACL;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC;QACA,OAAO;IACT;AACF;AAGO,MAAM,aAAa,OACxB,UACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,eAAe,UAAU;IAEjF,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,UAAkC;QACtC,gBAAgB;QAChB,GAAI,QAAQ,OAAO;IACrB;IAEA,IAAI,QAAQ;QACV,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ;IAC5C;IAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK;IAC5D,IAAI,QAAQ,IAAI,EAAE;QAChB,QAAQ,GAAG,CAAC,iBAAiB,QAAQ,IAAI;IAC3C;IAEA,IAAI,WAAW,MAAM,MAAM,KAAK;QAC9B,GAAG,OAAO;QACV;IACF;IAEA,6CAA6C;IAC7C,IAAI,SAAS,MAAM,KAAK,OAAO,QAAQ;QACrC,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM;QAEvB,IAAI,UAAU;YACZ,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU;YAC5C,WAAW,MAAM,MAAM,KAAK;gBAC1B,GAAG,OAAO;gBACV;YACF;QACF,OAAO;YACL,qCAAqC;YACrC,uCAAmC;;YAEnC;YACA,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE,EAAE;IAEhD,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAkB;YACtB,SAAS,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;YACvC,SAAS,KAAK,OAAO,IAAI;YACzB,QAAQ,SAAS,MAAM;QACzB;QACA,MAAM;IACR;IAEA,OAAO;AACT;AAGO,MAAM,UAAU;IACrB,kCAAkC;IAClC,YAAY,CAAC,cACX,WAA0B,sBAAsB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,mBAAmB;IACnB,YAAY,IACV,WAAiB;IAEnB,sBAAsB;IACtB,eAAe,CAAC,OACd,WAAiB,kBAAkB;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,OACf,WAAW,0BAA0B;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,SAAS;IACT,QAAQ,IACN,WAAW,iBAAiB;YAC1B,QAAQ;QACV;AACJ;AAGO,MAAM,YAAY;IACvB,qCAAqC;IACrC,WAAW,CAAC;QACV,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,QAAQ,EAAE,OAAO;IACtC;IAEA,oBAAoB;IACpB,gBAAgB,CAAC,cACf,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;IAEtC,sBAAsB;IACtB,mBAAmB,CAAC,aAAqB,OACvC,WAAW,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,EAAE;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,2BAA2B;IAC3B,gBAAgB,CAAC,aAAqB,OACpC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,CAAC,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,eAAe;IACf,aAAa,CAAC,aAAqB,OACjC,WAAW,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,EAAE;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,WAAW;IACtB,gBAAgB;IAChB,UAAU,CAAC;QACT,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,YAAY,EAAE,OAAO;IAC1C;IAEA,mBAAmB;IACnB,eAAe,CAAC,SACd,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAErC,cAAc;IACd,YAAY,CAAC,QAAgB,OAC3B,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,mBAAmB;IACnB,gBAAgB,CAAC,QAAgB,OAC/B,WAAW,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC,EAAE;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,eAAe;IAC1B,oBAAoB;IACpB,cAAc,CAAC;QACb,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,WAAW,EAAE,OAAO;IACzC;IAEA,uBAAuB;IACvB,mBAAmB,CAAC,aAClB,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAExC,sCAAsC;IACtC,4BAA4B,CAAC,YAAoB,OAC/C,WAAW,CAAC,WAAW,EAAE,WAAW,cAAc,CAAC,EAAE;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,+BAA+B;IAC/B,4BAA4B,CAAC,YAAoB,OAC/C,WAAW,CAAC,WAAW,EAAE,WAAW,cAAc,CAAC,EAAE;YACnD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,cAAc;IACzB,mBAAmB;IACnB,aAAa,CAAC;QACZ,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,oBAAoB,EAAE,OAAO;IAClD;IAEA,sBAAsB;IACtB,kBAAkB,CAAC,YACjB,WAAW,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;IAEhD,iBAAiB;IACjB,eAAe,CAAC,OACd,WAAW,wBAAwB;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,iBAAiB;IACjB,eAAe,CAAC,WAAmB,OACjC,WAAW,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,EAAE;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,iBAAiB;IACjB,eAAe,CAAC,YACd,WAAW,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC,EAAE;YAC9C,QAAQ;QACV;AACJ;AAGO,MAAM,gBAAgB;IAC3B,qBAAqB;IACrB,eAAe,CAAC;QACd,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,sBAAsB,EAAE,OAAO;IACpD;IAEA,uBAAuB;IACvB,mBAAmB,CAAC,aAClB,WAAW,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;IAEnD,kBAAkB;IAClB,gBAAgB,CAAC,OACf,WAAW,0BAA0B;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,YAAoB,OACnC,WAAW,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,aACf,WAAW,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC,EAAE;YACjD,QAAQ;QACV;AACJ;AAGO,MAAM,cAAc;IACzB,uBAAuB;IACvB,iBAAiB,CAAC;QAChB,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,uBAAuB,EAAE,OAAO;IACrD;IAEA,0BAA0B;IAC1B,sBAAsB,CAAC,gBACrB,WAAW,CAAC,uBAAuB,EAAE,cAAc,CAAC,CAAC;IAEvD,iBAAiB;IACjB,eAAe,CAAC,eAAuB,OACrC,WAAW,CAAC,uBAAuB,EAAE,cAAc,QAAQ,CAAC,EAAE;YAC5D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,eAAe;IAC1B,wDAAwD;IACxD,mBAAmB,IACjB,WAAW;IAEb,kDAAkD;IAClD,cAAc,IACZ,WAAW;AACf;AAGO,MAAM,gBAAgB;IAC3B,iBAAiB;IACjB,cAAc,CAAC;QACb,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,kBAAkB,EAAE,OAAO;IAChD;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,OACf,WAAW,sBAAsB;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,mBAAmB;IACnB,gBAAgB,CAAC,QAAgB,OAC/B,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,kBAAkB;IAClB,eAAe,CAAC,QAAgB,OAC9B,WAAW,CAAC,kBAAkB,EAAE,OAAO,OAAO,CAAC,EAAE;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,oBAAoB;IACpB,iBAAiB,CAAC;QAChB,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,qBAAqB,EAAE,OAAO;IACnD;AACF;AAGO,MAAM,aAAa;IACxB,kBAAkB;IAClB,YAAY,CAAC;QACX,MAAM,QAAQ,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,QAAQ,QAAQ,IAAI,GAAG;QACtE,OAAO,WAAW,CAAC,SAAS,EAAE,OAAO;IACvC;IAEA,qBAAqB;IACrB,iBAAiB,CAAC,WAChB,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAEpC,gBAAgB;IAChB,cAAc,CAAC,OACb,WAAW,aAAa;YACtB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,gBAAgB;IAChB,cAAc,CAAC,UAAkB,OAC/B,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,gBAAgB;IAChB,cAAc,CAAC,WACb,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE;YAClC,QAAQ;QACV;IAEF,uBAAuB;IACvB,oBAAoB,CAAC,UAAkB,OACrC,WAAW,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { User, AuthTokens, LoginResponse } from '@/types/api';\nimport { authApi, setTokens, getTokens, clearTokens } from '@/lib/api';\n\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: { email: string; password: string }) => Promise<void>;\n  logout: () => Promise<void>;\n  updateUser: (userData: Partial<User>) => void;\n  refreshUserProfile: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const isAuthenticated = !!user;\n\n  // Initialize auth state on mount\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const { refresh } = getTokens();\n      \n      if (refresh) {\n        try {\n          // Try to get user profile to verify token validity\n          const userProfile = await authApi.getProfile();\n          \n          // Ensure user is staff\n          if (userProfile.user_type !== 'STAFF') {\n            clearTokens();\n            throw new Error('Access denied: Staff access required');\n          }\n          \n          setUser(userProfile as User);\n        } catch (error) {\n          // Token is invalid, clear it\n          console.error('Auth initialization failed:', error);\n          clearTokens();\n        }\n      }\n      \n      setIsLoading(false);\n    };\n\n    initializeAuth();\n  }, []);\n\n  const login = async (credentials: { email: string; password: string }) => {\n    try {\n      const response = await authApi.loginStaff(credentials);\n      \n      // Ensure user is staff\n      if (response.user.user_type !== 'STAFF') {\n        throw new Error('Access denied: Staff access required');\n      }\n      \n      setTokens(response.tokens);\n      setUser(response.user);\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authApi.logout();\n    } catch (error) {\n      // Even if logout fails on server, clear local tokens\n      console.error('Logout error:', error);\n    } finally {\n      clearTokens();\n      setUser(null);\n    }\n  };\n\n  const updateUser = (userData: Partial<User>) => {\n    if (user) {\n      setUser({ ...user, ...userData });\n    }\n  };\n\n  const refreshUserProfile = async () => {\n    if (isAuthenticated) {\n      try {\n        const userProfile = await authApi.getProfile();\n        setUser(userProfile as User);\n      } catch (error) {\n        console.error('Failed to refresh user profile:', error);\n      }\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    isAuthenticated,\n    isLoading,\n    login,\n    logout,\n    updateUser,\n    refreshUserProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAC,CAAC;IAE1B,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD;YAE5B,IAAI,SAAS;gBACX,IAAI;oBACF,mDAAmD;oBACnD,MAAM,cAAc,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU;oBAE5C,uBAAuB;oBACvB,IAAI,YAAY,SAAS,KAAK,SAAS;wBACrC,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD;wBACV,MAAM,IAAI,MAAM;oBAClB;oBAEA,QAAQ;gBACV,EAAE,OAAO,OAAO;oBACd,6BAA6B;oBAC7B,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD;gBACZ;YACF;YAEA,aAAa;QACf;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YAE1C,uBAAuB;YACvB,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,SAAS;gBACvC,MAAM,IAAI,MAAM;YAClB;YAEA,CAAA,GAAA,iHAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM;YACzB,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,iHAAA,CAAA,UAAO,CAAC,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,qDAAqD;YACrD,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,CAAA,GAAA,iHAAA,CAAA,cAAW,AAAD;YACV,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM;YACR,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC;QACjC;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB;YACnB,IAAI;gBACF,MAAM,cAAc,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU;gBAC5C,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/vinay/Projects/Home_services/nextjs_staff/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}