(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{58:(e,s,r)=>{Promise.resolve().then(r.bind(r,2608))},858:(e,s,r)=>{"use strict";r.d(s,{N:()=>C});var t=r(5155),a=r(2115),n=r(6874),l=r.n(n),i=r(5695),c=r(7340),d=r(6151),m=r(7580),o=r(5670),x=r(7108),h=r(3332),g=r(1586),u=r(9074),f=r(2713),b=r(381),y=r(4416),j=r(1007),N=r(4835),v=r(4783),p=r(9434),w=r(283),A=r(3741);let k=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Orders",href:"/orders",icon:d.A},{name:"Customers",href:"/customers",icon:m.A},{name:"Providers",href:"/providers",icon:o.A},{name:"Services",href:"/services",icon:x.A},{name:"Categories",href:"/categories",icon:h.A},{name:"Payments",href:"/payments",icon:g.A},{name:"Scheduling",href:"/scheduling",icon:u.A},{name:"Analytics",href:"/analytics",icon:f.A},{name:"Settings",href:"/settings",icon:b.A}],C=e=>{let{children:s}=e,[r,n]=(0,a.useState)(!1),c=(0,i.usePathname)(),{user:d,logout:m}=(0,w.A)(),o=async()=>{try{await m()}catch(e){console.error("Logout failed:",e)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r&&(0,t.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>n(!1)}),(0,t.jsx)("div",{className:(0,p.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",r?"translate-x-0":"-translate-x-full"),children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Staff Dashboard"}),(0,t.jsx)("button",{onClick:()=>n(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:k.map(e=>{let s=c===e.href||c.startsWith(e.href+"/");return(0,t.jsxs)(l(),{href:e.href,className:(0,p.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>n(!1),children:[(0,t.jsx)(e.icon,{className:"w-5 h-5 mr-3"}),e.name]},e.name)})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(j.A,{className:"w-4 h-4 text-blue-600"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:null==d?void 0:d.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:null==d?void 0:d.email})]})]}),(0,t.jsxs)(A.$,{variant:"outline",size:"sm",fullWidth:!0,onClick:o,className:"text-red-600 border-red-200 hover:bg-red-50",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,t.jsx)("button",{onClick:()=>n(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(v.A,{className:"w-5 h-5"})}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",null==d?void 0:d.name]})})]})}),(0,t.jsx)("main",{className:"p-6",children:s})]})]})}},2608:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(5155);r(2115);var a=r(381),n=r(858),l=r(7703);function i(){return(0,t.jsx)(n.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(a.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,t.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"System Settings"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Configure system settings, user preferences, and application settings."})]})})})]})})}},3741:(e,s,r)=>{"use strict";r.d(s,{$:()=>l});var t=r(5155);r(2115);var a=r(9434),n=r(4338);let l=e=>{let{children:s,variant:r="primary",size:l="md",loading:i=!1,disabled:c=!1,className:d,onClick:m,type:o="button",fullWidth:x=!1}=e;return(0,t.jsxs)("button",{type:o,onClick:m,disabled:c||i,className:(0,a.cn)("inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[l],x&&"w-full",d),children:[i&&(0,t.jsx)(n.kt,{size:"sm",className:"mr-2"}),s]})}},7703:(e,s,r)=>{"use strict";r.d(s,{CN:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l});var t=r(5155);r(2115);var a=r(9434);let n=e=>{let{children:s,className:r,padding:n="md"}=e;return(0,t.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow border border-gray-200",{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[n],r),children:s})},l=e=>{let{children:s,className:r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("border-b border-gray-200 pb-4 mb-4",r),children:s})},i=e=>{let{children:s,className:r}=e;return(0,t.jsx)("h3",{className:(0,a.cn)("text-lg font-semibold text-gray-900",r),children:s})},c=e=>{let{children:s,className:r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("text-gray-600",r),children:s})},d=e=>{let{title:s,value:r,change:l,icon:i,className:c}=e;return(0,t.jsx)(n,{className:(0,a.cn)("",c),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:s}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:r}),l&&(0,t.jsxs)("p",{className:(0,a.cn)("text-sm font-medium","increase"===l.type?"text-green-600":"text-red-600"),children:["increase"===l.type?"↗":"↘"," ",l.value]})]}),i&&(0,t.jsx)("div",{className:"text-gray-400",children:i})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[743,952,441,684,358],()=>s(58)),_N_E=e.O()}]);