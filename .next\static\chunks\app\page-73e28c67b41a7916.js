(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2596:(e,r,t)=>{"use strict";function u(){for(var e,r,t=0,u="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,u,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(u=e(r[t]))&&(s&&(s+=" "),s+=u)}else for(u in r)r[u]&&(s&&(s+=" "),s+=u);return s}(e))&&(u&&(u+=" "),u+=r);return u}t.d(r,{$:()=>u})},3792:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var u=t(5155),s=t(2115),n=t(5695),o=t(283),a=t(4338);function f(){let{isAuthenticated:e,isLoading:r}=(0,o.A)(),t=(0,n.useRouter)();return(0,s.useEffect)(()=>{r||(e?t.push("/dashboard"):t.push("/auth/login"))},[e,r,t]),(0,u.jsx)(a.AV,{message:"Redirecting..."})}},5695:(e,r,t)=>{"use strict";var u=t(8999);t.o(u,"usePathname")&&t.d(r,{usePathname:function(){return u.usePathname}}),t.o(u,"useRouter")&&t.d(r,{useRouter:function(){return u.useRouter}})},5997:(e,r,t)=>{Promise.resolve().then(t.bind(t,3792))}},e=>{var r=r=>e(e.s=r);e.O(0,[952,441,684,358],()=>r(5997)),_N_E=e.O()}]);