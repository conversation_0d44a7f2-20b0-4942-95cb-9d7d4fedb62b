(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>n});var o=r(5155),a=r(2115),s=r(5731);let i=(0,a.createContext)(void 0),c=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},n=e=>{let{children:t}=e,[r,c]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),d=!!r;(0,a.useEffect)(()=>{(async()=>{let{refresh:e}=(0,s.RQ)();if(e)try{let e=await s.ZQ.getProfile();if("STAFF"!==e.user_type)throw(0,s.gf)(),Error("Access denied: Staff access required");c(e)}catch(e){console.error("Auth initialization failed:",e),(0,s.gf)()}l(!1)})()},[]);let h=async e=>{try{let t=await s.ZQ.loginStaff(e);if("STAFF"!==t.user.user_type)throw Error("Access denied: Staff access required");(0,s.h_)(t.tokens),c(t.user)}catch(e){throw console.error("Login failed:",e),e}},u=async()=>{try{await s.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{(0,s.gf)(),c(null)}},f=async()=>{if(d)try{let e=await s.ZQ.getProfile();c(e)}catch(e){console.error("Failed to refresh user profile:",e)}};return(0,o.jsx)(i.Provider,{value:{user:r,isAuthenticated:d,isLoading:n,login:h,logout:u,updateUser:e=>{r&&c({...r,...e})},refreshUserProfile:f},children:t})}},347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},4380:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2093,23)),Promise.resolve().then(r.t.bind(r,7735,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,283))},5731:(e,t,r)=>{"use strict";r.d(t,{DW:()=>u,LP:()=>f,RQ:()=>a,ZQ:()=>l,cN:()=>d,dG:()=>h,gf:()=>i,h_:()=>s});let o="http://localhost:8000/api",a=()=>({access:localStorage.getItem("access_token")||"",refresh:localStorage.getItem("refresh_token")||""}),s=e=>{localStorage.setItem("access_token",e.access),localStorage.setItem("refresh_token",e.refresh)},i=()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token")},c=async()=>{let{refresh:e}=a();if(!e)return null;try{let t=await fetch("".concat(o,"/auth/token/refresh/"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refresh:e})});if(!t.ok)return i(),null;{let r=await t.json();return s({access:r.access,refresh:e}),r.access}}catch(e){return console.error("Token refresh failed:",e),i(),null}},n=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.startsWith("http")?e:"".concat(o).concat(e),{access:s}=a(),i={"Content-Type":"application/json",...t.headers};s&&(i.Authorization="Bearer ".concat(s)),console.log("API Request: ".concat(t.method||"GET"," ").concat(r)),t.body&&console.log("Request body:",t.body);let n=await fetch(r,{...t,headers:i});if(401===n.status&&s){console.log("Token expired, attempting refresh...");let e=await c();if(e)i.Authorization="Bearer ".concat(e),n=await fetch(r,{...t,headers:i});else throw window.location.href="/auth/login",Error("Authentication failed")}let l=await n.json();if(console.log("API Response: ".concat(n.status),l),!n.ok)throw{message:l.message||l.error||"An error occurred",details:l.details||l,status:n.status};return l},l={loginStaff:e=>n("/auth/login/email/",{method:"POST",body:JSON.stringify(e)}),getProfile:()=>n("/auth/profile/"),updateProfile:e=>n("/auth/profile/",{method:"PUT",body:JSON.stringify(e)}),changePassword:e=>n("/auth/change-password/",{method:"POST",body:JSON.stringify(e)}),logout:()=>n("/auth/logout/",{method:"POST"})},d={getOrders:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return n("/orders/".concat(t))},getOrderDetail:e=>n("/orders/".concat(e,"/")),updateOrderStatus:(e,t)=>n("/orders/".concat(e,"/status/"),{method:"POST",body:JSON.stringify(t)}),assignProvider:(e,t)=>n("/orders/".concat(e,"/assign-provider/"),{method:"POST",body:JSON.stringify(t)}),cancelOrder:(e,t)=>n("/orders/".concat(e,"/cancel/"),{method:"POST",body:JSON.stringify(t)})},h={getUsers:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return n("/auth/users/".concat(t))},getUserDetail:e=>n("/auth/users/".concat(e,"/")),updateUser:(e,t)=>n("/auth/users/".concat(e,"/"),{method:"PUT",body:JSON.stringify(t)}),toggleUserLock:(e,t)=>n("/auth/users/".concat(e,"/toggle-lock/"),{method:"POST",body:JSON.stringify(t)})},u={getProviders:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return n("/providers/".concat(t))},getProviderDetail:e=>n("/providers/".concat(e,"/")),updateProviderVerification:(e,t)=>n("/providers/".concat(e,"/verification/"),{method:"POST",body:JSON.stringify(t)}),toggleProviderAvailability:(e,t)=>n("/providers/".concat(e,"/availability/"),{method:"POST",body:JSON.stringify(t)})},f={getDashboardStats:()=>n("/orders/dashboard/"),getUserStats:()=>n("/auth/admin/user-stats/")}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,441,684,358],()=>t(4380)),_N_E=e.O()}]);