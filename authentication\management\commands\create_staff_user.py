from django.core.management.base import BaseCommand
from authentication.models import User


class Command(BaseCommand):
    help = 'Create or update a staff user for testing'

    def add_arguments(self, parser):
        parser.add_argument('--mobile', type=str, default='+918328315313', help='Mobile number')
        parser.add_argument('--name', type=str, default='Staff User', help='User name')
        parser.add_argument('--email', type=str, default='<EMAIL>', help='Email address')

    def handle(self, *args, **options):
        mobile = options['mobile']
        name = options['name']
        email = options['email']

        try:
            # Try to get existing user
            user = User.objects.get(mobile_number=mobile)
            self.stdout.write(f"Found existing user: {user.name}")
            
            # Update user to be staff
            user.user_type = 'STAFF'
            user.is_staff = True
            user.is_active = True
            user.is_verified = True
            user.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'Updated user {user.name} to STAFF type')
            )
            
        except User.DoesNotExist:
            # Create new staff user
            user = User.objects.create_user(
                mobile_number=mobile,
                name=name,
                email=email,
                user_type='STAFF',
                is_staff=True,
                is_active=True,
                is_verified=True
            )
            user.set_password('password123')  # Set a default password
            user.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'Created new staff user: {user.name}')
            )

        # Print user details
        self.stdout.write(f"User Details:")
        self.stdout.write(f"  Name: {user.name}")
        self.stdout.write(f"  Mobile: {user.mobile_number}")
        self.stdout.write(f"  Email: {user.email}")
        self.stdout.write(f"  User Type: {user.user_type}")
        self.stdout.write(f"  Is Staff: {user.is_staff}")
        self.stdout.write(f"  Is Active: {user.is_active}")
        self.stdout.write(f"  Is Verified: {user.is_verified}")
