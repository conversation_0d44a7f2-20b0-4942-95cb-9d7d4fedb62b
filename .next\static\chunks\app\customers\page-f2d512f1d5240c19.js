(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},8830:(e,s,t)=>{Promise.resolve().then(t.bind(t,9022))},9022:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(5155),l=t(2115),r=t(4616),i=t(7924),c=t(1007),d=t(8883),n=t(9420),x=t(2657),m=t(3717),o=t(858),h=t(7703),u=t(3741),j=t(2814),v=t(4338),p=t(5731),f=t(9434);function y(){let[e,s]=(0,l.useState)([]),[t,y]=(0,l.useState)(!0),[N,g]=(0,l.useState)(""),[b,w]=(0,l.useState)(""),[_,A]=(0,l.useState)(""),[C,S]=(0,l.useState)({count:0,next:null,previous:null});(0,l.useEffect)(()=>{k()},[b,_]);let k=async()=>{try{y(!0);let e={user_type:"CUSTOMER",ordering:"-date_joined",limit:20};b&&(e.search=b),_&&(e.is_active="active"===_);let t=await p.dG.getUsers(e);s(t.results),S({count:t.count,next:t.next,previous:t.previous})}catch(e){g(e.message||"Failed to load customers"),console.error("Customers fetch error:",e)}finally{y(!1)}},z=async(e,s)=>{try{await p.dG.updateUser(e,{is_active:!s}),k()}catch(e){console.error("Status update error:",e),alert("Failed to update customer status")}};return t?(0,a.jsx)(o.N,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Customers"}),(0,a.jsx)(v.B0,{})]})}):(0,a.jsx)(o.N,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Customers"}),(0,a.jsxs)(u.$,{children:[(0,a.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"Add Customer"]})]}),(0,a.jsx)(h.Zp,{children:(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search customers...",value:b,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:_,onChange:e=>A(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"})]})})]})})}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{children:["Customers (",C.count,")"]})}),(0,a.jsx)(h.Wu,{children:N?(0,a.jsx)("div",{className:"text-center py-8 text-red-600",children:N}):0===e.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No customers found"}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Customer"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Contact"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Verified"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Joined"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"space-y-1",children:[e.email&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2"}),e.email]}),e.mobile_number&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),e.mobile_number]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)(j.W,{status:e.is_active?"active":"inactive"})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)(j.W,{status:e.is_verified?"verified":"pending"})}),(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(0,f.r6)(e.date_joined)}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,f.fw)(e.date_joined)})]}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(x.A,{className:"w-4 h-4"})}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(m.A,{className:"w-4 h-4"})}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>z(e.id,e.is_active),children:e.is_active?"Deactivate":"Activate"})]})})]},e.id))})]})})})]}),C.count>20&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",e.length," of ",C.count," customers"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(u.$,{variant:"outline",size:"sm",disabled:!C.previous,children:"Previous"}),(0,a.jsx)(u.$,{variant:"outline",size:"sm",disabled:!C.next,children:"Next"})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[743,952,419,441,684,358],()=>s(8830)),_N_E=e.O()}]);