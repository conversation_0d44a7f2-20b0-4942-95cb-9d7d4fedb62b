"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[419],{858:(e,s,r)=>{r.d(s,{N:()=>C});var t=r(5155),a=r(2115),l=r(6874),n=r.n(l),i=r(5695),c=r(7340),d=r(6151),o=r(7580),m=r(5670),x=r(7108),h=r(3332),g=r(1586),u=r(9074),f=r(2713),b=r(381),y=r(4416),p=r(1007),v=r(4835),j=r(4783),N=r(9434),w=r(283),A=r(3741);let k=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Orders",href:"/orders",icon:d.A},{name:"Customers",href:"/customers",icon:o.A},{name:"Providers",href:"/providers",icon:m.A},{name:"Services",href:"/services",icon:x.A},{name:"Categories",href:"/categories",icon:h.A},{name:"Payments",href:"/payments",icon:g.A},{name:"Scheduling",href:"/scheduling",icon:u.A},{name:"Analytics",href:"/analytics",icon:f.A},{name:"Settings",href:"/settings",icon:b.A}],C=e=>{let{children:s}=e,[r,l]=(0,a.useState)(!1),c=(0,i.usePathname)(),{user:d,logout:o}=(0,w.A)(),m=async()=>{try{await o()}catch(e){console.error("Logout failed:",e)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[r&&(0,t.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>l(!1)}),(0,t.jsx)("div",{className:(0,N.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",r?"translate-x-0":"-translate-x-full"),children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Staff Dashboard"}),(0,t.jsx)("button",{onClick:()=>l(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:k.map(e=>{let s=c===e.href||c.startsWith(e.href+"/");return(0,t.jsxs)(n(),{href:e.href,className:(0,N.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>l(!1),children:[(0,t.jsx)(e.icon,{className:"w-5 h-5 mr-3"}),e.name]},e.name)})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(p.A,{className:"w-4 h-4 text-blue-600"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:null==d?void 0:d.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:null==d?void 0:d.email})]})]}),(0,t.jsxs)(A.$,{variant:"outline",size:"sm",fullWidth:!0,onClick:m,className:"text-red-600 border-red-200 hover:bg-red-50",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,t.jsx)("button",{onClick:()=>l(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(j.A,{className:"w-5 h-5"})}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",null==d?void 0:d.name]})})]})}),(0,t.jsx)("main",{className:"p-6",children:s})]})]})}},2657:(e,s,r)=>{r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2814:(e,s,r)=>{r.d(s,{E:()=>l,W:()=>n});var t=r(5155);r(2115);var a=r(9434);let l=e=>{let{children:s,variant:r="default",size:l="md",className:n}=e;return(0,t.jsx)("span",{className:(0,a.cn)("inline-flex items-center font-medium rounded-full",{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800"}[r],{sm:"px-2 py-1 text-xs",md:"px-2.5 py-0.5 text-sm",lg:"px-3 py-1 text-base"}[l],n),children:s})},n=e=>{let{status:s,className:r}=e;return(0,t.jsx)(l,{variant:{pending:"warning",confirmed:"info",assigned:"info",in_progress:"info",completed:"success",cancelled:"error",refunded:"default",paid:"success",failed:"error",active:"success",inactive:"default",locked:"error",verified:"success",rejected:"error",available:"success",unavailable:"error",blocked:"error"}[s.toLowerCase()]||"default",className:r,children:s.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ")})}},3741:(e,s,r)=>{r.d(s,{$:()=>n});var t=r(5155);r(2115);var a=r(9434),l=r(4338);let n=e=>{let{children:s,variant:r="primary",size:n="md",loading:i=!1,disabled:c=!1,className:d,onClick:o,type:m="button",fullWidth:x=!1}=e;return(0,t.jsxs)("button",{type:m,onClick:o,disabled:c||i,className:(0,a.cn)("inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],x&&"w-full",d),children:[i&&(0,t.jsx)(l.kt,{size:"sm",className:"mr-2"}),s]})}},4616:(e,s,r)=>{r.d(s,{A:()=>t});let t=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},7703:(e,s,r)=>{r.d(s,{CN:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>n});var t=r(5155);r(2115);var a=r(9434);let l=e=>{let{children:s,className:r,padding:l="md"}=e;return(0,t.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow border border-gray-200",{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[l],r),children:s})},n=e=>{let{children:s,className:r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("border-b border-gray-200 pb-4 mb-4",r),children:s})},i=e=>{let{children:s,className:r}=e;return(0,t.jsx)("h3",{className:(0,a.cn)("text-lg font-semibold text-gray-900",r),children:s})},c=e=>{let{children:s,className:r}=e;return(0,t.jsx)("div",{className:(0,a.cn)("text-gray-600",r),children:s})},d=e=>{let{title:s,value:r,change:n,icon:i,className:c}=e;return(0,t.jsx)(l,{className:(0,a.cn)("",c),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:s}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:r}),n&&(0,t.jsxs)("p",{className:(0,a.cn)("text-sm font-medium","increase"===n.type?"text-green-600":"text-red-600"),children:["increase"===n.type?"↗":"↘"," ",n.value]})]}),i&&(0,t.jsx)("div",{className:"text-gray-400",children:i})]})})}},7924:(e,s,r)=>{r.d(s,{A:()=>t});let t=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8883:(e,s,r)=>{r.d(s,{A:()=>t});let t=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,r)=>{r.d(s,{A:()=>t});let t=(0,r(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}}]);