"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[952],{283:(e,t,r)=>{r.d(t,{A:()=>i,AuthProvider:()=>c});var o=r(5155),a=r(2115),s=r(5731);let n=(0,a.createContext)(void 0),i=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[c,l]=(0,a.useState)(!0),d=!!r;(0,a.useEffect)(()=>{(async()=>{let{refresh:e}=(0,s.RQ)();if(e)try{let e=await s.ZQ.getProfile();if("STAFF"!==e.user_type)throw(0,s.gf)(),Error("Access denied: Staff access required");i(e)}catch(e){console.error("Auth initialization failed:",e),(0,s.gf)()}l(!1)})()},[]);let u=async e=>{try{let t=await s.ZQ.loginStaff(e);if("STAFF"!==t.user.user_type)throw Error("Access denied: Staff access required");(0,s.h_)(t.tokens),i(t.user)}catch(e){throw console.error("Login failed:",e),e}},h=async()=>{try{await s.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{(0,s.gf)(),i(null)}},g=async()=>{if(d)try{let e=await s.ZQ.getProfile();i(e)}catch(e){console.error("Failed to refresh user profile:",e)}};return(0,o.jsx)(n.Provider,{value:{user:r,isAuthenticated:d,isLoading:c,login:u,logout:h,updateUser:e=>{r&&i({...r,...e})},refreshUserProfile:g},children:t})}},4338:(e,t,r)=>{r.d(t,{AV:()=>n,B0:()=>i,kt:()=>s});var o=r(5155);r(2115);var a=r(9434);let s=e=>{let{size:t="md",className:r}=e;return(0,o.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[t],r)})},n=e=>{let{message:t="Loading..."}=e;return(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)(s,{size:"lg",className:"mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-gray-600",children:t})]})})},i=e=>{let{className:t}=e;return(0,o.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow p-6",t),children:(0,o.jsxs)("div",{className:"animate-pulse",children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})})}},5731:(e,t,r)=>{r.d(t,{DW:()=>h,LP:()=>g,RQ:()=>a,ZQ:()=>l,cN:()=>d,dG:()=>u,gf:()=>n,h_:()=>s});let o="http://localhost:8000/api",a=()=>({access:localStorage.getItem("access_token")||"",refresh:localStorage.getItem("refresh_token")||""}),s=e=>{localStorage.setItem("access_token",e.access),localStorage.setItem("refresh_token",e.refresh)},n=()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token")},i=async()=>{let{refresh:e}=a();if(!e)return null;try{let t=await fetch("".concat(o,"/auth/token/refresh/"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refresh:e})});if(!t.ok)return n(),null;{let r=await t.json();return s({access:r.access,refresh:e}),r.access}}catch(e){return console.error("Token refresh failed:",e),n(),null}},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.startsWith("http")?e:"".concat(o).concat(e),{access:s}=a(),n={"Content-Type":"application/json",...t.headers};s&&(n.Authorization="Bearer ".concat(s)),console.log("API Request: ".concat(t.method||"GET"," ").concat(r)),t.body&&console.log("Request body:",t.body);let c=await fetch(r,{...t,headers:n});if(401===c.status&&s){console.log("Token expired, attempting refresh...");let e=await i();if(e)n.Authorization="Bearer ".concat(e),c=await fetch(r,{...t,headers:n});else throw window.location.href="/auth/login",Error("Authentication failed")}let l=await c.json();if(console.log("API Response: ".concat(c.status),l),!c.ok)throw{message:l.message||l.error||"An error occurred",details:l.details||l,status:c.status};return l},l={loginStaff:e=>c("/auth/login/email/",{method:"POST",body:JSON.stringify(e)}),getProfile:()=>c("/auth/profile/"),updateProfile:e=>c("/auth/profile/",{method:"PUT",body:JSON.stringify(e)}),changePassword:e=>c("/auth/change-password/",{method:"POST",body:JSON.stringify(e)}),logout:()=>c("/auth/logout/",{method:"POST"})},d={getOrders:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return c("/orders/".concat(t))},getOrderDetail:e=>c("/orders/".concat(e,"/")),updateOrderStatus:(e,t)=>c("/orders/".concat(e,"/status/"),{method:"POST",body:JSON.stringify(t)}),assignProvider:(e,t)=>c("/orders/".concat(e,"/assign-provider/"),{method:"POST",body:JSON.stringify(t)}),cancelOrder:(e,t)=>c("/orders/".concat(e,"/cancel/"),{method:"POST",body:JSON.stringify(t)})},u={getUsers:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return c("/auth/users/".concat(t))},getUserDetail:e=>c("/auth/users/".concat(e,"/")),updateUser:(e,t)=>c("/auth/users/".concat(e,"/"),{method:"PUT",body:JSON.stringify(t)}),toggleUserLock:(e,t)=>c("/auth/users/".concat(e,"/toggle-lock/"),{method:"POST",body:JSON.stringify(t)})},h={getProviders:e=>{let t=e?"?".concat(new URLSearchParams(e).toString()):"";return c("/providers/".concat(t))},getProviderDetail:e=>c("/providers/".concat(e,"/")),updateProviderVerification:(e,t)=>c("/providers/".concat(e,"/verification/"),{method:"POST",body:JSON.stringify(t)}),toggleProviderAvailability:(e,t)=>c("/providers/".concat(e,"/availability/"),{method:"POST",body:JSON.stringify(t)})},g={getDashboardStats:()=>c("/orders/dashboard/"),getUserStats:()=>c("/auth/admin/user-stats/")}},9434:(e,t,r)=>{r.d(t,{Xq:()=>c,cn:()=>a,fw:()=>i,r6:()=>n,vv:()=>s});var o=r(2596);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.$)(t)}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"₹",r="string"==typeof e?parseFloat(e):e;return isNaN(r)?"".concat(t,"0.00"):"".concat(t).concat(r.toLocaleString("en-IN",{minimumFractionDigits:2,maximumFractionDigits:2}))}function n(e){return("string"==typeof e?new Date(e):e).toLocaleString("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function i(e){let t="string"==typeof e?new Date(e):e,r=Math.floor((new Date().getTime()-t.getTime())/1e3);if(r<60)return"Just now";let o=Math.floor(r/60);if(o<60)return"".concat(o," minute").concat(o>1?"s":""," ago");let a=Math.floor(o/60);if(a<24)return"".concat(a," hour").concat(a>1?"s":""," ago");let s=Math.floor(a/24);return s<7?"".concat(s," day").concat(s>1?"s":""," ago"):("string"==typeof t?new Date(t):t).toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",...void 0})}function c(e){let t="string"==typeof e?parseFloat(e):e;return isNaN(t)?"0.0":t.toFixed(1)}}}]);