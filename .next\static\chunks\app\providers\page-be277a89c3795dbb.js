(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[387],{646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5855:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(5155),l=a(2115),r=a(4616),i=a(7924),c=a(1007),d=a(8883),n=a(9420),x=a(9946);let o=(0,x.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var m=a(2657),h=a(646);let u=(0,x.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var j=a(858),v=a(7703),p=a(3741),f=a(2814),y=a(4338),N=a(5731),g=a(9434);function b(){let[e,s]=(0,l.useState)([]),[a,x]=(0,l.useState)(!0),[b,w]=(0,l.useState)(""),[_,A]=(0,l.useState)(""),[k,S]=(0,l.useState)(""),[P,z]=(0,l.useState)({count:0,next:null,previous:null});(0,l.useEffect)(()=>{C()},[_,k]);let C=async()=>{try{x(!0);let e={ordering:"-created_at",limit:20};_&&(e.search=_),k&&(e.verification_status=k);let a=await N.DW.getProviders(e);s(a.results),z({count:a.count,next:a.next,previous:a.previous})}catch(e){w(e.message||"Failed to load providers"),console.error("Providers fetch error:",e)}finally{x(!1)}},W=async(e,s)=>{try{await N.DW.updateProviderVerification(e,{verification_status:s}),C()}catch(e){console.error("Verification update error:",e),alert("Failed to update provider verification")}},$=async(e,s)=>{try{await N.DW.toggleProviderAvailability(e,{is_available:!s}),C()}catch(e){console.error("Availability update error:",e),alert("Failed to update provider availability")}};return a?(0,t.jsx)(j.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Service Providers"}),(0,t.jsx)(y.B0,{})]})}):(0,t.jsx)(j.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Service Providers"}),(0,t.jsxs)(p.$,{children:[(0,t.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"Add Provider"]})]}),(0,t.jsx)(v.Zp,{children:(0,t.jsx)(v.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,t.jsx)("input",{type:"text",placeholder:"Search providers...",value:_,onChange:e=>A(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})}),(0,t.jsx)("div",{className:"sm:w-48",children:(0,t.jsxs)("select",{value:k,onChange:e=>S(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"verified",children:"Verified"}),(0,t.jsx)("option",{value:"rejected",children:"Rejected"})]})})]})})}),(0,t.jsxs)(v.Zp,{children:[(0,t.jsx)(v.aR,{children:(0,t.jsxs)(v.ZB,{children:["Service Providers (",P.count,")"]})}),(0,t.jsx)(v.Wu,{children:b?(0,t.jsx)("div",{className:"text-center py-8 text-red-600",children:b}):0===e.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No providers found"}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Provider"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Contact"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Verification"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Rating"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Orders"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:e.map(e=>(0,t.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(c.A,{className:"w-5 h-5 text-gray-600"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.user_name}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.business_name||"No business name"})]})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"space-y-1",children:[e.user_email&&(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),e.user_email]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2"}),e.user_mobile]})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsx)(f.W,{status:e.verification_status})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,t.jsx)("span",{className:"font-medium",children:(0,g.Xq)(e.rating)}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 ml-1",children:["(",e.total_reviews,")"]})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("div",{className:"font-medium",children:e.total_orders_completed}),(0,t.jsx)("div",{className:"text-gray-500",children:"completed"})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsx)(f.W,{status:e.is_available?"available":"unavailable"})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(m.A,{className:"w-4 h-4"})}),"pending"===e.verification_status&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.$,{variant:"ghost",size:"sm",onClick:()=>W(e.id,"verified"),className:"text-green-600",children:(0,t.jsx)(h.A,{className:"w-4 h-4"})}),(0,t.jsx)(p.$,{variant:"ghost",size:"sm",onClick:()=>W(e.id,"rejected"),className:"text-red-600",children:(0,t.jsx)(u,{className:"w-4 h-4"})})]}),(0,t.jsx)(p.$,{variant:"ghost",size:"sm",onClick:()=>$(e.id,e.is_available),children:e.is_available?"Disable":"Enable"})]})})]},e.id))})]})})})]}),P.count>20&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",e.length," of ",P.count," providers"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(p.$,{variant:"outline",size:"sm",disabled:!P.previous,children:"Previous"}),(0,t.jsx)(p.$,{variant:"outline",size:"sm",disabled:!P.next,children:"Next"})]})]})]})})}},9237:(e,s,a)=>{Promise.resolve().then(a.bind(a,5855))}},e=>{var s=s=>e(e.s=s);e.O(0,[743,952,419,441,684,358],()=>s(9237)),_N_E=e.O()}]);