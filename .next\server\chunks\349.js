exports.id=349,exports.ids=[349],exports.modules={1135:()=>{},2185:(e,t,r)=>{"use strict";r.d(t,{DW:()=>u,LP:()=>f,RQ:()=>s,ZQ:()=>l,cN:()=>h,dG:()=>c,gf:()=>n,h_:()=>i});let o="http://localhost:8000/api",s=()=>({access:"",refresh:""}),i=e=>{},n=()=>{},a=async()=>{let{refresh:e}=s();if(!e)return null;try{let t=await fetch(`${o}/auth/token/refresh/`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refresh:e})});if(!t.ok)return n(),null;{let r=await t.json();return i({access:r.access,refresh:e}),r.access}}catch(e){return console.error("Token refresh failed:",e),n(),null}},d=async(e,t={})=>{let r=e.startsWith("http")?e:`${o}${e}`,{access:i}=s(),n={"Content-Type":"application/json",...t.headers};i&&(n.Authorization=`Bearer ${i}`),console.log(`API Request: ${t.method||"GET"} ${r}`),t.body&&console.log("Request body:",t.body);let d=await fetch(r,{...t,headers:n});if(401===d.status&&i){console.log("Token expired, attempting refresh...");let e=await a();if(e)n.Authorization=`Bearer ${e}`,d=await fetch(r,{...t,headers:n});else throw Error("Authentication failed")}let l=await d.json();if(console.log(`API Response: ${d.status}`,l),!d.ok)throw{message:l.message||l.error||"An error occurred",details:l.details||l,status:d.status};return l},l={loginStaff:e=>d("/auth/login/email/",{method:"POST",body:JSON.stringify(e)}),getProfile:()=>d("/auth/profile/"),updateProfile:e=>d("/auth/profile/",{method:"PUT",body:JSON.stringify(e)}),changePassword:e=>d("/auth/change-password/",{method:"POST",body:JSON.stringify(e)}),logout:()=>d("/auth/logout/",{method:"POST"})},h={getOrders:e=>{let t=e?`?${new URLSearchParams(e).toString()}`:"";return d(`/orders/${t}`)},getOrderDetail:e=>d(`/orders/${e}/`),updateOrderStatus:(e,t)=>d(`/orders/${e}/status/`,{method:"POST",body:JSON.stringify(t)}),assignProvider:(e,t)=>d(`/orders/${e}/assign-provider/`,{method:"POST",body:JSON.stringify(t)}),cancelOrder:(e,t)=>d(`/orders/${e}/cancel/`,{method:"POST",body:JSON.stringify(t)})},c={getUsers:e=>{let t=e?`?${new URLSearchParams(e).toString()}`:"";return d(`/auth/users/${t}`)},getUserDetail:e=>d(`/auth/users/${e}/`),updateUser:(e,t)=>d(`/auth/users/${e}/`,{method:"PUT",body:JSON.stringify(t)}),toggleUserLock:(e,t)=>d(`/auth/users/${e}/toggle-lock/`,{method:"POST",body:JSON.stringify(t)})},u={getProviders:e=>{let t=e?`?${new URLSearchParams(e).toString()}`:"";return d(`/providers/${t}`)},getProviderDetail:e=>d(`/providers/${e}/`),updateProviderVerification:(e,t)=>d(`/providers/${e}/verification/`,{method:"POST",body:JSON.stringify(t)}),toggleProviderAvailability:(e,t)=>d(`/providers/${e}/availability/`,{method:"POST",body:JSON.stringify(t)})},f={getDashboardStats:()=>d("/orders/dashboard/"),getUserStats:()=>d("/auth/admin/user-stats/")}},3061:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>d});var o=r(687),s=r(3210),i=r(2185);let n=(0,s.createContext)(void 0),a=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,s.useState)(null),[a,d]=(0,s.useState)(!0),l=!!t;(0,s.useEffect)(()=>{(async()=>{let{refresh:e}=(0,i.RQ)();if(e)try{let e=await i.ZQ.getProfile();if("STAFF"!==e.user_type)throw(0,i.gf)(),Error("Access denied: Staff access required");r(e)}catch(e){console.error("Auth initialization failed:",e),(0,i.gf)()}d(!1)})()},[]);let h=async e=>{try{let t=await i.ZQ.loginStaff(e);if("STAFF"!==t.user.user_type)throw Error("Access denied: Staff access required");(0,i.h_)(t.tokens),r(t.user)}catch(e){throw console.error("Login failed:",e),e}},c=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{(0,i.gf)(),r(null)}},u=async()=>{if(l)try{let e=await i.ZQ.getProfile();r(e)}catch(e){console.error("Failed to refresh user profile:",e)}};return(0,o.jsx)(n.Provider,{value:{user:t,isAuthenticated:l,isLoading:a,login:h,logout:c,updateUser:e=>{t&&r({...t,...e})},refreshUserProfile:u},children:e})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>l});var o=r(7413),s=r(2376),i=r.n(s),n=r(8726),a=r.n(n);r(1135);var d=r(9131);let l={title:"Home Services - Staff Dashboard",description:"Staff management dashboard for Home Services platform"};function h({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${i().variable} ${a().variable} antialiased bg-gray-50`,children:(0,o.jsx)(d.AuthProvider,{children:e})})})}},6613:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7012:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var o=r(2907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\contexts\\AuthContext.tsx","useAuth");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Documents\\vinay\\Projects\\Home_services\\nextjs_staff\\src\\contexts\\AuthContext.tsx","AuthProvider")},9748:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))}};