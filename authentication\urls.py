from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'authentication'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.RegisterView.as_view(), name='register'),
    path('register/mobile/', views.MobileRegisterView.as_view(), name='mobile_register'),
    path('login/email/', views.EmailLoginView.as_view(), name='email_login'),
    path('login/mobile/', views.MobileLoginView.as_view(), name='mobile_login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    
    # OTP endpoints
    path('otp/send/', views.SendOTPView.as_view(), name='send_otp'),
    path('otp/resend/', views.ResendOTPView.as_view(), name='resend_otp'),
    path('otp/verify/', views.VerifyOTPView.as_view(), name='verify_otp'),
    
    # Token management
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # User profile
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # Address management
    path('addresses/', views.AddressListCreateView.as_view(), name='address_list_create'),
    path('addresses/<int:pk>/', views.AddressDetailView.as_view(), name='address_detail'),
    
    # Admin endpoints
    path('admin/failed-attempts/', views.AdminFailedAttemptsView.as_view(), name='admin_failed_attempts'),
    path('admin/unlock-account/', views.AdminUnlockAccountView.as_view(), name='admin_unlock_account'),
    path('admin/reset-rate-limit/', views.AdminResetRateLimitView.as_view(), name='admin_reset_rate_limit'),
    path('admin/user-stats/', views.AdminUserStatsView.as_view(), name='admin_user_stats'),
    path('users/', views.AdminUsersListView.as_view(), name='admin_users_list'),

    # Health check endpoint
    path('health/', views.HealthCheckView.as_view(), name='health_check'),
]
