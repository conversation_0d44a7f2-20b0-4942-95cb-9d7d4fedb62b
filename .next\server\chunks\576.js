"use strict";exports.id=576,exports.ids=[576],exports.modules={149:(e,s,r)=>{r.d(s,{N:()=>$});var t=r(687),a=r(3210),n=r(5814),i=r.n(n),l=r(6189),c=r(2192),d=r(1057),o=r(1312),m=r(3508),x=r(9080),g=r(7360),h=r(5778),u=r(228),f=r(3411),b=r(4027),y=r(1860),N=r(8869),v=r(83),j=r(2941),p=r(4780),w=r(3213),A=r(2643);let k=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Orders",href:"/orders",icon:d.A},{name:"Customers",href:"/customers",icon:o.A},{name:"Providers",href:"/providers",icon:m.A},{name:"Services",href:"/services",icon:x.A},{name:"Categories",href:"/categories",icon:g.A},{name:"Payments",href:"/payments",icon:h.A},{name:"Scheduling",href:"/scheduling",icon:u.A},{name:"Analytics",href:"/analytics",icon:f.A},{name:"Settings",href:"/settings",icon:b.A}],$=({children:e})=>{let[s,r]=(0,a.useState)(!1),n=(0,l.usePathname)(),{user:c,logout:d}=(0,w.A)(),o=async()=>{try{await d()}catch(e){console.error("Logout failed:",e)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s&&(0,t.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>r(!1)}),(0,t.jsx)("div",{className:(0,p.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",s?"translate-x-0":"-translate-x-full"),children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Staff Dashboard"}),(0,t.jsx)("button",{onClick:()=>r(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:k.map(e=>{let s=n===e.href||n.startsWith(e.href+"/");return(0,t.jsxs)(i(),{href:e.href,className:(0,p.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>r(!1),children:[(0,t.jsx)(e.icon,{className:"w-5 h-5 mr-3"}),e.name]},e.name)})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(N.A,{className:"w-4 h-4 text-blue-600"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:c?.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:c?.email})]})]}),(0,t.jsxs)(A.$,{variant:"outline",size:"sm",fullWidth:!0,onClick:o,className:"text-red-600 border-red-200 hover:bg-red-50",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsx)("div",{className:"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,t.jsx)("button",{onClick:()=>r(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:(0,t.jsx)(j.A,{className:"w-5 h-5"})}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Welcome back, ",c?.name]})})]})}),(0,t.jsx)("main",{className:"p-6",children:e})]})]})}},440:(e,s,r)=>{r.r(s),r.d(s,{default:()=>a});var t=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2643:(e,s,r)=>{r.d(s,{$:()=>i});var t=r(687);r(3210);var a=r(4780),n=r(9776);let i=({children:e,variant:s="primary",size:r="md",loading:i=!1,disabled:l=!1,className:c,onClick:d,type:o="button",fullWidth:m=!1})=>(0,t.jsxs)("button",{type:o,onClick:d,disabled:l||i,className:(0,a.cn)("inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[r],m&&"w-full",c),children:[i&&(0,t.jsx)(n.kt,{size:"sm",className:"mr-2"}),e]})},4780:(e,s,r)=>{r.d(s,{Xq:()=>c,cn:()=>a,fw:()=>l,r6:()=>i,vv:()=>n});var t=r(9384);function a(...e){return(0,t.$)(e)}function n(e,s="₹"){let r="string"==typeof e?parseFloat(e):e;return isNaN(r)?`${s}0.00`:`${s}${r.toLocaleString("en-IN",{minimumFractionDigits:2,maximumFractionDigits:2})}`}function i(e){return("string"==typeof e?new Date(e):e).toLocaleString("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function l(e){let s="string"==typeof e?new Date(e):e,r=Math.floor((new Date().getTime()-s.getTime())/1e3);if(r<60)return"Just now";let t=Math.floor(r/60);if(t<60)return`${t} minute${t>1?"s":""} ago`;let a=Math.floor(t/60);if(a<24)return`${a} hour${a>1?"s":""} ago`;let n=Math.floor(a/24);return n<7?`${n} day${n>1?"s":""} ago`:("string"==typeof s?new Date(s):s).toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",...void 0})}function c(e){let s="string"==typeof e?parseFloat(e):e;return isNaN(s)?"0.0":s.toFixed(1)}},8749:(e,s,r)=>{r.d(s,{CN:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var t=r(687);r(3210);var a=r(4780);let n=({children:e,className:s,padding:r="md"})=>(0,t.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow border border-gray-200",{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[r],s),children:e}),i=({children:e,className:s})=>(0,t.jsx)("div",{className:(0,a.cn)("border-b border-gray-200 pb-4 mb-4",s),children:e}),l=({children:e,className:s})=>(0,t.jsx)("h3",{className:(0,a.cn)("text-lg font-semibold text-gray-900",s),children:e}),c=({children:e,className:s})=>(0,t.jsx)("div",{className:(0,a.cn)("text-gray-600",s),children:e}),d=({title:e,value:s,change:r,icon:i,className:l})=>(0,t.jsx)(n,{className:(0,a.cn)("",l),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s}),r&&(0,t.jsxs)("p",{className:(0,a.cn)("text-sm font-medium","increase"===r.type?"text-green-600":"text-red-600"),children:["increase"===r.type?"↗":"↘"," ",r.value]})]}),i&&(0,t.jsx)("div",{className:"text-gray-400",children:i})]})})},9776:(e,s,r)=>{r.d(s,{AV:()=>i,B0:()=>l,kt:()=>n});var t=r(687);r(3210);var a=r(4780);let n=({size:e="md",className:s})=>(0,t.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e],s)}),i=({message:e="Loading..."})=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(n,{size:"lg",className:"mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:e})]})}),l=({className:e})=>(0,t.jsx)("div",{className:(0,a.cn)("bg-white rounded-lg shadow p-6",e),children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-4"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]})})}};